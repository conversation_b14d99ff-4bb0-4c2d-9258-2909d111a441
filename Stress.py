
import PublicFuc
import configparser
import csv
import os,re,ParseExcel
from openpyxl.utils import get_column_letter
from openpyxl.drawing.image import Image
from openpyxl.styles import  PatternFill,Alignment,Border,Side,colors,Font
from datetime import datetime,timedelta
from openpyxl.utils import get_column_letter,column_index_from_string

#以下变量全部用于汇总
por_sample_cnt = 0
por_sample_maxtime = ''
spor_sample_cnt = 0
spor_sample_maxtime = ''

def Run(curpath, workBook, alignment):
    ws = workBook['压力']
    ws.alignment = alignment
    
    ProPlan27MultiPartitionStress(curpath, ws,8)
    
    PublicFuc.WriteReportTime(ws,'E',2)
    PublicFuc.WriteReportOperator(ws,'H',2)


#填写封面
def ProEntry(workBook):
    ws = workBook['Full Test Reports']
   
    ws['J27'] = spor_sample_cnt
    ws['K27'] = spor_sample_maxtime 
    ws['J29'] = por_sample_cnt
    ws['K29'] = por_sample_maxtime
   

#获取规范化字典中的SPOR或POR中的最长测试时间
def GetMaxTime(dic):
    maxTimeInSeconds = 0
    for sampleNo in dic:
        row = dic[sampleNo]
        timestr = row[12] #SPOR和POR时间所在列
        if timestr != '':
            timedata = timestr.split(':')
            totalSecond = int(timedata[0])*3600 + int(timedata[1])*60 + int(timedata[2])
            if totalSecond > maxTimeInSeconds:
                maxTimeInSeconds = totalSecond

    if maxTimeInSeconds == 0:
        return ''

    hour = int(maxTimeInSeconds/3600)
    lefSeconds = maxTimeInSeconds%3600
    minutes = int(lefSeconds/60)
    seconds = lefSeconds%60
    strTime = '%d:%d:%d'%(hour,minutes,seconds)
    return strTime

config = configparser.RawConfigParser()
def ReadMarsIniDataEx(curpath, pattern, dataDic, caseName, recordCnt = 2):
    unitLst = ['M/s']
    for file in PublicFuc.fileLst:
        if not re.match(pattern, file):
            continue
        #必须清除
        config.clear()
        config.read(file,encoding='gbk')
        if 'HWCONFIG' not in config.sections() or caseName not in config.sections():
            continue
        keyName = config['HWCONFIG']['mms_flash']
        if keyName not in dataDic:
            #0表示样片数量不确定，需要获取全部数据
            if recordCnt == len(dataDic) and 0 != recordCnt:
                continue
            dataDic[keyName] = {}
            dataDic[keyName]['capacity'] = config['HWCONFIG']['capacity']

            dataDic[keyName]['MMS_PC'] = config['HWCONFIG']['MMS_PC']

            fileMdTime = os.path.getmtime(file)
            dataDic[keyName][GetTimeKeyName(caseName)] = fileMdTime

            for key in config[caseName]:
                value = config[caseName][key]
                #去除单位
                for unit in unitLst:
                    if unit in value:
                        value = value[:value.find(unit)]
                        break
                dataDic[keyName][key] = value

            resultStr = GetValueFromDic(dataDic[keyName],'test_result')
            if 'TRUE'== resultStr:
                dataDic[keyName]['test_result'] = 'TRUE'
            elif 'unfinished' == resultStr:
                dataDic[keyName]['test_result'] = 'UNFINISHED' #测试中的情况单列，既不能作为测试通过，也不能作为测试fail，保留原样。
            else:
                dataDic[keyName]['test_result'] = resultStr
                filemt= time.localtime(os.stat(file).st_mtime)  
                strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
                errDiskLst.append([keyName,dataDic[keyName]['MMS_PC'],resultStr,strTime])
            dataDic[keyName]['file_path'] = file
        else:
            #重复样片，取最新的
            oldTime = dataDic[keyName][GetTimeKeyName(caseName)]
            fileMdTime = os.path.getmtime(file)
            if fileMdTime < oldTime:
                continue#数据不是新的，不做读取覆盖
            
            dataDic[keyName][GetTimeKeyName(caseName)] = fileMdTime

            #dataDic[keyName] = {}
            dataDic[keyName]['capacity'] = config['HWCONFIG']['capacity']

            dataDic[keyName]['MMS_PC'] = config['HWCONFIG']['MMS_PC']


            for key in config[caseName]:
                value = config[caseName][key]
                #去除单位
                for unit in unitLst:
                    if unit in value:
                        value = value[:value.find(unit)]
                        break
                dataDic[keyName][key] = value

            resultStr = GetValueFromDic(dataDic[keyName],'test_result')
            if 'TRUE'== resultStr:
                dataDic[keyName]['test_result'] = 'TRUE'
            elif 'unfinished' == resultStr:
                dataDic[keyName]['test_result'] = 'UNFINISHED' #测试中的情况单列，既不能作为测试通过，也不能作为测试fail，保留原样。
            else:
                dataDic[keyName]['test_result'] = resultStr
                filemt= time.localtime(os.stat(file).st_mtime)  
                strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
                errDiskLst.append([keyName,dataDic[keyName]['MMS_PC'],resultStr,strTime])
            dataDic[keyName]['file_path'] = file

#按照json列写数据
def WriteDataNormal(worksheet, startLine, dataDic, colLst, keyLst, lineCnt = 1,resultColNameList = []):
    curLine = startLine
    for key in dataDic:
        line = dataDic[key]
        for index,col in enumerate(colLst):
            try:
                if 0 == index:
                    #第一列是编号，直接填key
                    worksheet['%s%d'%(col, curLine)] = key
                else:
                    worksheet['%s%d'%(col, curLine)] = line[index-1]
                    if 'A5-A6' == keyLst[index-1]:
                        if line[index-1] >= 400:
                            worksheet['%s%d'%(col, curLine)].fill = warnFill
                    if col in resultColNameList:
                        strResult = line[index-1].upper()
                        if strResult == 'UNFINISHED':
                            #填写独特的颜色
                            worksheet['%s%d'%(col, curLine)].fill = unfinishedFill
                        elif strResult != 'TRUE' and strResult != 'PASS' and strResult != '':
                            worksheet['%s%d'%(col, curLine)].fill = warnFill
                worksheet['%s%d'%(col, curLine)].alignment = alignment
            #合并的单元格只能写一次，需要捕获异常
            except(AttributeError):
                continue
        curLine += lineCnt


smart_info_key_map = {'WL_SLC_MAX':'WL SLC MAX','WL_SLC_MIN':'WL SLC MIN','WL_SLC_AVG':'WL SLC AVG',
                      'WL_TLC_MAX':'WL TLC MAX','WL_TLC_MIN':'WL TLC MIN','WL_TLC_AVG':'WL TLC AVG'} # 6285的字段映射




# 在ProPOR函数中调用示例
# WriteMarsDataToSheet(g_DataDic, 'IOZone', '1.POR(Safe Power On Reset)', ws)
    

#Plan27-四分区多线程FIO和BIT
def ProPlan27MultiPartitionStress(curpath,worksheet,recordCnt):

    pattern = '.+\\\\Plan27\\\\T_INS_SD_A041\\\\Smart_Read_Begin\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'Smart' #读取Smart信息
    PublicFuc.ReadMarsIniData_New(PublicFuc.g_DataDic, '压力','1.  4分区老化测试，测试前置条件：H2+文件拷贝满盘Pass', 'Pre-Smart',caseName, 'SMART_DATA_ITEMS','SMART_DATA_ITEMS',curpath, pattern,'','',0)

    pattern = '.+\\\\Plan27\\\\T_INS_SD_A041\\\\多分区FIO_Rand_4KWrite_168H\\\\\d{14}\\\\.+\\\\report.ini$'
    PublicFuc.ReadFioIniData_New(PublicFuc.g_DataDic, '压力','1.  4分区老化测试，测试前置条件：H2+文件拷贝满盘Pass', 'fio 4K随机老化测试196H','FIO_DATA_ITEMS',curpath, pattern,'',0)

    pattern = '.+\\\\Plan27\\\\T_INS_SD_A041\\\\多分区FIO_Rand_1KWrite_168H\\\\\d{14}\\\\.+\\\\report.ini$'
    PublicFuc.ReadFioIniData_New(PublicFuc.g_DataDic, '压力','1.  4分区老化测试，测试前置条件：H2+文件拷贝满盘Pass', 'fio 1K随机老化测试196H','FIO_DATA_ITEMS',curpath, pattern,'',0)

    pattern = '.+\\\\Plan27\\\\T_INS_SD_A041\\\\多分区FIO_Seq_128KWrite_168H\\\\\d{14}\\\\.+\\\\report.ini$'
    PublicFuc.ReadFioIniData_New(PublicFuc.g_DataDic, '压力','1.  4分区老化测试，测试前置条件：H2+文件拷贝满盘Pass', 'fio 128K顺序老化196H','FIO_DATA_ITEMS',curpath, pattern,'',0)

    pattern = '.+\\\\Plan27\\\\T_INS_SD_A041\\\\多分区BIT测试Default168小时\\\\\d{14}\\\\.+\\\\report.ini$'
    PublicFuc.ReadQaIniData_New(PublicFuc.g_DataDic, '压力','1.  4分区老化测试，测试前置条件：H2+文件拷贝满盘Pass', 'BurnInTest\n(Test disk=10%，File size=1%，Block size=1024K）','QA_BIT_DATA_ITEMS',curpath, pattern,'',0)

    pattern = '.+\\\\Plan27\\\\T_INS_SD_A041\\\\Smart_Read_End\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'Smart' #读取Smart信息
    PublicFuc.ReadMarsIniData_New(PublicFuc.g_DataDic, '压力','1.  4分区老化测试，测试前置条件：H2+文件拷贝满盘Pass', 'After-Smart',caseName, 'SMART_DATA_ITEMS','SMART_DATA_ITEMS',curpath, pattern,'','',0)

    # 计算Delta Smart Information
    PublicFuc.CalculateDeltaSmartInformation(PublicFuc.g_DataDic, '压力', '1.  4分区老化测试，测试前置条件：H2+文件拷贝满盘Pass','Pre-Smart','After-Smart')
    PublicFuc.WriteMarsDataToSheet(PublicFuc.g_DataDic, '压力', '1.  4分区老化测试，测试前置条件：H2+文件拷贝满盘Pass', worksheet)

#写标号
def FillNo(worksheet,startLine,newDic):
    for i in range(len(newDic)):
        worksheet['%s%d'%(get_column_letter(1), startLine+i)] = i+1

def IsFloat(str):
    try:
        val = float(str)
        return True
    except:
        return False

def GetConclusion(newDic,keyLst):
    result_H2_2_1 = 'FALSE'
    result_H2_2_2 = 'FALSE'
    result_CopyFile = 'FALSE'
    result_POR = 'FALSE'
    wavg_H2_1_2 = 0
    wavg_H2_2_2 = 0
    conclusion = False
    for key in newDic:
        line = newDic[key]
        for index,col in enumerate(keyLst):
            if col == 'result_H2_2_1':
                result_H2_2_1 = line[index]
            if col == 'result_H2_2_2':
                result_H2_2_2 = line[index]
            if col == 'result_CopyFile':
                result_CopyFile = line[index]
            if col == 'result_POR':
                result_POR = line[index]
            if col == 'wavg_H2_1_2':
                wavg_H2_1_2 = line[index]
            if col == 'wavg_H2_2_2':
                wavg_H2_2_2 = line[index]

        if result_H2_2_1 == 'UNFINISHED' or result_H2_2_2 == 'UNFINISHED' or result_POR == 'UNFINISHED' or result_CopyFile == 'UNFINISHED':
            conclusion = 'UNFINISHED'
        else:
            if result_H2_2_1 != 'TRUE' or result_H2_2_2 != 'TRUE' or result_POR != 'TRUE' or result_CopyFile != 'TRUE':
                conclusion = False

            if IsFloat(wavg_H2_2_2) and IsFloat(wavg_H2_1_2):
                ratio = float(wavg_H2_2_2)/float(wavg_H2_1_2)
                if ratio < 0.95:
                    conclusion = False
                else:
                    conclusion = True
            else:
                conclusion = False

        if conclusion == True:
            newDic[key].append('PASS')
        elif conclusion == 'UNFINISHED':
            newDic[key].append('UNFINISHED')
        else:
            newDic[key].append('FAIL')

        
