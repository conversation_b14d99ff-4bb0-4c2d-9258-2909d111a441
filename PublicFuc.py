import configparser
import csv,time
import os,re
from openpyxl.utils import get_column_letter,column_index_from_string
from openpyxl.drawing.image import Image
from openpyxl.styles import  PatternFill,Alignment,Border,Side,colors
from datetime import datetime,timedelta
import ErrDiskInfo,ParseExcel
from win32com.client import Dispatch

MPDATA_DIR = ""
REPORT_DIR = ""

minKey = ['Read Acc Time']
alignment = Alignment(horizontal='center',vertical='center')
warnFill = PatternFill('solid', fgColor='FF0000')
unfinishedFill = PatternFill('solid', fgColor='FFFF33')
maxFill = PatternFill('solid', fgColor='64C8FF')
commonSmartKey = ['F1','F2','A5','A6','05','0C','A3','A4','A7','AF','B2','B5','B6','C0','C3','C4','C5','C6','C7']

errDiskLst = []
fileLst = []
config = configparser.RawConfigParser()
def GetAllFile(curpath):
    for dirpath,dirnames,filenames in os.walk(curpath):
        for filename in filenames:
            fullname = os.path.join(dirpath, filename)
            fileLst.append(fullname)

def WriteErrDiskFile(strFile):
    if 0 != len(errDiskLst):
        with open(strFile,'w+') as file: 
            for errLst in errDiskLst:
                strErr = '样片:%s    PC:%s    Err:%s    Time:%s\n'%(errLst[0],errLst[1],errLst[2],errLst[3])
                file.write(strErr)


def ReadYSMpegToolCsvData(curpath, pattern, dataDic):
    fileIdx = 1
    for file in fileLst:
        if not re.match(pattern, file):
            continue
        pos = file.rfind('\\')
        imagepath = file[:pos]
        #必须清除

        file_data = []

        # 尝试多种编码方式来读取文件
        encodings = ['cp936', 'gb18030', 'utf-8', 'gbk', 'utf-16', 'latin-1']
        csvfile = None

        for encoding in encodings:
            try:
                csvfile = open(file, encoding=encoding, errors='ignore')
                csv_reader = csv.reader(csvfile)  # 使用csv.reader读取csvfile中的文件
                try:
                    birth_header = next(csv_reader)  # 读取第一行每一列的标题
                    break  # 成功读取，跳出编码循环
                except:
                    csvfile.close()
                    continue
            except UnicodeDecodeError:
                if csvfile:
                    csvfile.close()
                continue
            except Exception as e:
                if csvfile:
                    csvfile.close()
                print(f"读取文件 {file} 时发生错误: {e}")
                continue

        if csvfile is None:
            print(f"无法读取文件 {file}，尝试了所有编码方式")
            continue

        try:
            for row in csv_reader:  # 将csv 文件中的数据保存到file_data中
                file_data.append(row)
        except Exception as e:
            print(f"处理文件 {file} 数据时发生错误: {e}")
        finally:
            csvfile.close()

        dataDic[fileIdx]=file_data
        fileIdx += 1


#1个样片多条记录
def WriteCsvData(worksheet, startLine, dataDic):
    curLine = startLine

    rowIdx = 1
    for key in dataDic:
        for line in dataDic[key]:
            for col in range(len(line)+1):#columCnt + 1
                try:
                    if 0 == col:
                        #第一列是编号，直接填行号
                        worksheet['%s%d'%(get_column_letter(1), curLine)] = rowIdx
                    elif 1 == col:
                        worksheet['%s%d'%(get_column_letter(col+1), curLine)] = line[1]
                    elif 2 == col:
                        worksheet['%s%d'%(get_column_letter(col+1), curLine)] = line[0]
                    else:
                        worksheet['%s%d'%(get_column_letter(col+1), curLine)] = line[col-1]
                        worksheet['%s%d'%(get_column_letter(col+1), curLine)]
                    worksheet['%s%d'%(get_column_letter(col+1), curLine)].alignment = alignment
                    worksheet['%s%d'%(get_column_letter(col+1), curLine)].border = my_border('thin', 'thin', 'thin', 'thin')
                    
                #合并的单元格只能写一次，需要捕获异常
                except(AttributeError):
                    continue
            curLine += 1
            rowIdx += 1

    return  curLine   


    #定义边框样式
def my_border(t_border, b_border, l_border, r_border):
    border = Border(top=Side(border_style=t_border, color=colors.BLACK),
                    bottom=Side(border_style=b_border, color=colors.BLACK),
                    left=Side(border_style=l_border, color=colors.BLACK),
                    right=Side(border_style=r_border, color=colors.BLACK))
    return border


#--------------以下内容为SD配置方式实现报告统计的关键公共程序----------------------------

MARS_H2_DATA_ITEMS = ['AVERAGE_WRITE_VEL','AVERAGE_READ_VEL','TEST_TIME','TEST_RESULT']
MARS_H2_DATA_ITEMS_NO_TIME = ['AVERAGE_WRITE_VEL','AVERAGE_READ_VEL','TEST_RESULT']
MARS_COPY_FILE =['TEST_TIME','TEST_RESULT']
MARS_POR = ['TEST_TIME','POWERDOWN_CNT','TEST_RESULT']
MARS_IOZONE = ['TEST_TIME','TEST_RESULT']
MARS_HCTEST = ['TEST_TIME','TESET_CIRCLE','TEST_RESULT']
MARS_BURNIN = ['TEST_TIME','TESET_CIRCLE','TEST_RESULT']
MARS_TIME_RESULT =['TEST_TIME','TEST_RESULT'] #公用于所有2项结果的测试
MARS_TIME_CIRCLE_RESULT = ['TEST_TIME','TESET_CIRCLE','TEST_RESULT'] #公共的用于所有Mars的这种情况
MARS_W_R_TIME_CYCLE_RESULT =['AVERAGE_WRITE_VEL','AVERAGE_READ_VEL','TEST_TIME','TESET_CIRCLE','TEST_RESULT']
MARS_W_R_TIME_RESULT =['AVERAGE_WRITE_VEL','AVERAGE_READ_VEL','TEST_TIME','TEST_RESULT']
MARS_PC_W_R_TIME_RESULT = ['pc_no','AVERAGE_WRITE_VEL','AVERAGE_READ_VEL','TEST_TIME','TEST_RESULT']

# QATOOL测试相关数据项列表
QA_BIT_DATA_ITEMS = ['Duration','BitCycle','qa_err_msg']  #['Cap','pc_no','Duration','BitCycle','qa_err_msg']
QA_CDM_DATA_ITEMS = ['Cap_MB','pc_no','format','SeqQ32T1_Read','SeqQ32T1_Write','4KQ32T1_Read','4KQ32T1_Write','Seq_Read','Seq_Write','4K_Read','4K_Write','qa_err_msg']
QA_H2_DATA_ITEMS = ['Cap','write speed','read speed','qa_err_msg']
QA_HDBENCH_DATA_ITEMS = ['Cap','Read','Write','RRead','RWrite','qa_err_msg']
QA_PERFORMANCE_DATA_ITEMS = ['Cap','pc_no','format','SeqQ32T1_Read','SeqQ32T1_Write','4KiBQ8T8_Read','4KiBQ8T8_Write','4KiBQ32T1_Read','4KiBQ32T1_Write','4KiBQ1T1_Read','4KiBQ1T1_Write','qa_err_msg']

SMART_DATA_ITEMS = ['DataCRCErrCnt','DebugErrCode','CmdCRCErrCnt','WatchDogCnt','SLCBadBlock_New','TLCBadBlock_New','RetryCnt','VDTCnt','WAF','Write_Data','SLC_FMinBlkLen','TLC_FMinBlkLen','PowerUpCnt','WL_SLC_MAX','WL_SLC_MIN','WL_SLC_AVG','WL_TLC_MAX','WL_TLC_MIN','WL_TLC_AVG','SLC_DIFF','TLC_DIFF','WEAR_SLC_TLC','MaxCurrentControllerTemp','MAX_DoneBlockLen','DoneBlockError','SLCInheritOverFlow','TLCInheritOverFlow','SLC_Lifetime','TLC_Lifetime']
SMART_DELTA_ITEMS =['WatchDogCnt','SLCBadBlock_New','TLCBadBlock_New','RetryCnt','VDTCnt','Write_Data','PowerUpCnt','SLC_Lifetime','TLC_Lifetime']

FIO_DATA_ITEMS = ['MAX_WRITE_SPEED','MIN_WRITE_SPEED','AVG_WRITE_SPEED','MAX_READ_SPEED','MIN_READ_SPEED','AVG_READ_SPEED','TEST_RESULT'] #

UNDEFINE = 'inf' #除0的情况下的无效值

#存放全局数据的地方
g_DataDic = {}
g_ExcelJsonDic = {}

class ListManager:
    def __init__(self):
        self.registry = {}
    
    def register_list(self, name, items):
        self.registry[name] = items
    
    def get_list(self, name):
        return self.registry.get(name)
    
    def process_list(self, name):
        lst = self.get_list(name)
        if lst:
            print(f"列表 '{name}' 的内容:", lst)
            lst.append(len(lst) + 1)  # 示例操作：追加元素
        else:
            print(f"列表 '{name}' 未注册")

#从所有的配置中读取配置文件
def LoadAllJsonConfig(reportpath):
    try:
        g_ExcelJsonDic.clear()
        coordinates_list = ParseExcel.load_table_coordinates(reportpath + '\\config\\Stress.json')
        g_ExcelJsonDic['压力'] = coordinates_list
    except:
        print(traceback.format_exc())
        return

#LoadAllJsonConfig()

# 使用示例
manager = ListManager()
manager.register_list("MARS_H2_DATA_ITEMS", MARS_H2_DATA_ITEMS)
manager.register_list("MARS_H2_DATA_ITEMS_NO_TIME", MARS_H2_DATA_ITEMS_NO_TIME)

manager.register_list("MARS_COPY_FILE", MARS_COPY_FILE)
manager.register_list("MARS_POR", MARS_POR)
manager.register_list("MARS_IOZONE", MARS_IOZONE)
manager.register_list("MARS_HCTEST", MARS_HCTEST)
manager.register_list("MARS_BURNIN", MARS_BURNIN)
manager.register_list("MARS_TIME_RESULT", MARS_TIME_RESULT) #公用于所有2项结果的测试
manager.register_list("MARS_TIME_CIRCLE_RESULT", MARS_TIME_CIRCLE_RESULT)
manager.register_list("MARS_W_R_TIME_CYCLE_RESULT", MARS_W_R_TIME_CYCLE_RESULT)
manager.register_list("MARS_W_R_TIME_RESULT", MARS_W_R_TIME_RESULT)
manager.register_list("MARS_PC_W_R_TIME_RESULT", MARS_PC_W_R_TIME_RESULT)


# 注册QA测试数据项列表
manager.register_list("QA_BIT_DATA_ITEMS", QA_BIT_DATA_ITEMS)
manager.register_list("QA_CDM_DATA_ITEMS", QA_CDM_DATA_ITEMS)
manager.register_list("QA_H2_DATA_ITEMS", QA_H2_DATA_ITEMS)
manager.register_list("QA_HDBENCH_DATA_ITEMS", QA_HDBENCH_DATA_ITEMS)
manager.register_list("QA_PERFORMANCE_DATA_ITEMS", QA_PERFORMANCE_DATA_ITEMS)

manager.register_list("SMART_DATA_ITEMS", SMART_DATA_ITEMS)
manager.register_list("SMART_DELTA_ITEMS", SMART_DELTA_ITEMS)

#注册AterEx一些特殊测试项目
manager.register_list("FIO_DATA_ITEMS", FIO_DATA_ITEMS)



print(manager.get_list("MARS_H2_DATA_ITEMS"))  # 输出: [1, 2, 3, 4]



#从SSD那边拷贝过来的
def ReadQaIniDataEx(curpath, pattern, dataDic, keyLst, imageSuffix, recordCnt = 10, diskCnt = 2):
    unitLst = ['MB/s', 'ms', '%']
    for file in fileLst:
        if not re.match(pattern, file):
            continue
        pos = file.rfind('\\')
        imagepath = file[:pos]
        #必须清除
        config.clear()
        config.read(file,encoding = 'gbk')
        for sec in config.sections():
            if sec not in dataDic:
                #diskCnt为0表示统计所有磁盘数据
                if diskCnt == len(dataDic) and 0 != diskCnt:
                    continue
                dataDic[sec] = {}
                dataDic[sec]['data'] = []
                fileMdTime = os.path.getmtime(file)
                dataDic[sec]['_file_time'] = fileMdTime

            tempLst = []
            pcNo = ''

            oldTime = dataDic[sec]['_file_time']
            fileMdTime = os.path.getmtime(file)
            if fileMdTime < oldTime - 1:#减掉1是为了避免第一次的数据因为时间精度卡点问题未被统计，减1。
                continue#数据不是新的，不做读取覆盖

            dataDic[sec]['_file_time'] = fileMdTime

            if 'pc_no' in config[sec]:
                pcNo = config[sec]['pc_no']
            for key in keyLst:
                if key.lower() in config[sec]:
                    value = config[sec][key.lower()]
                    #去除单位
                    for unit in unitLst:
                        if unit in value:
                            value = value[:value.find(unit)]
                            break
                    tempLst.append(value)
                    if 'qa_err_msg' == key:
                        filemt= time.localtime(os.stat(file).st_mtime)  
                        strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
                        errDiskLst.append([sec,pcNo,value,strTime])
                else:
                    tempLst.append('')
            if len(dataDic[sec]['data']) < recordCnt and [] != tempLst:
                #imageSuffix为空不需要截图，只需要数据
                if '' != imageSuffix:
                    image = imagepath+'\\%s_%s'%(sec, imageSuffix)
                    if os.path.isfile(image):
                        tempLst.append(image)
                    else:
                        tempLst.append('')
                dataDic[sec]['data'].append(tempLst)

def ReadQaIniData(curpath, pattern, dataDic, caseKey, keyLst, imageSuffix, diskCnt = 6):
    unitLst = ['MByte/s', 'MB/s', 'ms', '%']
    for file in fileLst:
        if not re.match(pattern, file):
            continue
        pos = file.rfind('\\')
        imagepath = file[:pos]
        #必须清除
        config.clear()
        config.read(file,encoding = 'gbk')
        for sec in config.sections():
            if sec not in dataDic:
                #diskCnt为0表示统计所有磁盘数据
                if diskCnt == len(dataDic) and 0 != diskCnt:
                    continue
                dataDic[sec] = {}
            if caseKey not in dataDic[sec]:
                #代表此种数据为此样片的第一次数据
                tempLst = []
                pcNo = ''
                fileMdTime = os.path.getmtime(file)
                dataDic[sec][GetTimeKeyName(caseKey)] = fileMdTime
                if 'pc_no' in config[sec]:
                    pcNo = config[sec]['pc_no']
                for key in keyLst:
                    if key.lower() in config[sec]:
                        value = config[sec][key.lower()]
                        #去除单位
                        for unit in unitLst:
                            if unit in value:
                                value = value[:value.find(unit)]
                                break
                        tempLst.append(value)
                        if 'qa_err_msg' == key:
                            filemt= time.localtime(os.stat(file).st_mtime)  
                            strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
                            errDiskLst.append([sec,pcNo,value,strTime])
                            errCode = value.upper()
                            if errCode != '' and errCode != 'PASS' and errCode != 'TRUE':
                                if caseKey == 'HT_BIT':
                                    AppendErrDiskInfo('HT BIT_Err',sec,value,pcNo,file)
                                elif caseKey == 'LT_BIT':
                                    AppendErrDiskInfo('LT BIT_Err',sec,value,pcNo,file)
                                
                    else:
                        tempLst.append('')
                #imageSuffix为空不需要截图，只需要数据
                if '' != imageSuffix:
                    image = imagepath+'\\%s_%s'%(sec, imageSuffix)
                    if os.path.isfile(image):
                        tempLst.append(image)
                    else:
                        tempLst.append('')
                dataDic[sec][caseKey] = tempLst
            else:
                #代表此种数据为此样片再次遇到的数据，此时如果遇到的是最新数据，需要覆盖。
                tempLst = []
                pcNo = ''

                oldTime = dataDic[sec][GetTimeKeyName(caseKey)]
                fileMdTime = os.path.getmtime(file)
                if fileMdTime < oldTime:
                    continue#数据不是新的，不做读取覆盖

                dataDic[sec][GetTimeKeyName(caseKey)] = fileMdTime

                if 'pc_no' in config[sec]:
                    pcNo = config[sec]['pc_no']
                for key in keyLst:
                    if key.lower() in config[sec]:
                        value = config[sec][key.lower()]
                        #去除单位
                        for unit in unitLst:
                            if unit in value:
                                value = value[:value.find(unit)]
                                break
                        tempLst.append(value)
                        if 'qa_err_msg' == key:
                            filemt= time.localtime(os.stat(file).st_mtime)  
                            strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
                            errDiskLst.append([sec,pcNo,value,strTime])
                            errCode = value.upper()
                            if errCode != '' and errCode != 'PASS' and errCode != 'TRUE':
                                if caseKey == 'HT_BIT':
                                    AppendErrDiskInfo('HT BIT_Err',sec,value,pcNo,file)
                                elif caseKey == 'LT_BIT':
                                    AppendErrDiskInfo('LT BIT_Err',sec,value,pcNo,file)
                                
                    else:
                        tempLst.append('')
                #imageSuffix为空不需要截图，只需要数据
                if '' != imageSuffix:
                    image = imagepath+'\\%s_%s'%(sec, imageSuffix)
                    if os.path.isfile(image):
                        tempLst.append(image)
                    else:
                        tempLst.append('')
                dataDic[sec][caseKey] = tempLst

def ReadMarsIniData(curpath, pattern, dataDic, caseName, caseKey, keyLst, imageSuffix, diskCnt = 6):
    unitLst = ['M/s']
    for file in fileLst:
        if not re.match(pattern, file):
            continue
        #必须清除
        config.clear()
        config.read(file,encoding = 'gbk')
        if 'HWCONFIG' not in config.sections():
            continue
        keyName = config['HWCONFIG']['mms_flash']
        if keyName not in dataDic:
            #0表示样片数量不确定，需要获取全部数据
            if diskCnt == len(dataDic) and 0 != diskCnt:
                continue
            dataDic[keyName] = {}
        if caseName not in config.sections():
            continue

        dataDic[keyName]['MMS_PC'] = config['HWCONFIG']['MMS_PC']
        if caseKey not in dataDic[keyName]:
            fileMdTime = os.path.getmtime(file)
            dataDic[keyName][GetTimeKeyName(caseKey)] = fileMdTime
            dataDic[keyName]['capacity'] = config['HWCONFIG']['capacity']
            if 'test_result' in config[caseName]:
                dataDic[keyName]['test_result'] = config[caseName]['test_result']
            tempLst = []
            for key in keyLst:
                if 'Cap' == key:
                    cap = config['HWCONFIG']['capacity']
                    if '' == cap:
                        tempLst.append('')
                    else:
                        tempLst.append(str(int(float(cap))))
                    continue
                if key.lower() in config[caseName]:
                    value = config[caseName][key.lower()]
                    #去除单位
                    for unit in unitLst:
                        if unit in value:
                            value = value[:value.find(unit)]
                            break
                    tempLst.append(value)
                else:
                    tempLst.append('')

            testResult =  GetValueFromDic(dataDic[keyName],'test_result','FAIL')
            if 'TRUE'== testResult:
                dataDic[keyName]['test_result'] = 'TRUE'
            else:
                dataDic[keyName]['test_result'] = testResult
                filemt= time.localtime(os.stat(file).st_mtime)  
                strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
                errDiskLst.append([keyName,dataDic[keyName]['MMS_PC'],testResult,strTime])
                if testResult != '' and testResult != 'PASS' and testResult != 'TRUE':
                    if caseName == 'AT_H2':
                        AppendErrDiskInfo('H2_Err',keyName,testResult,config['HWCONFIG']['MMS_PC'],file)
                    elif caseName == 'AT_CopyFile':
                        AppendErrDiskInfo('Copy File_Err',keyName,testResult,config['HWCONFIG']['MMS_PC'],file)

            #imageSuffix为空不需要截图，只需要数据
            if '' != imageSuffix:
                pos = file.rfind('\\report')
                imagepath = file[:pos]
                image = ''
                for i in os.listdir(imagepath):
                    if i.endswith('.bmp'):
                        image = os.path.join(imagepath,i)
                        break
                if os.path.isfile(image):
                    tempLst.append(image)
                else:
                    tempLst.append('')
            dataDic[keyName][caseKey] = tempLst
        else:
            #如果样片已经存在，需要检查是否是新数据，新数据才覆盖
            oldTime = dataDic[keyName][GetTimeKeyName(caseKey)]
            fileMdTime = os.path.getmtime(file)
            if fileMdTime < oldTime:
                continue#数据不是新的，不做读取覆盖

            dataDic[keyName][GetTimeKeyName(caseKey)] = fileMdTime

            dataDic[keyName]['capacity'] = config['HWCONFIG']['capacity']
            if 'test_result' in config[caseName]:
                dataDic[keyName]['test_result'] = config[caseName]['test_result']
            tempLst = []
            for key in keyLst:
                if 'Cap' == key:
                    cap = config['HWCONFIG']['capacity']
                    if '' == cap:
                        tempLst.append('')
                    else:
                        tempLst.append(str(int(float(cap))))
                    continue
                if key.lower() in config[caseName]:
                    value = config[caseName][key.lower()]
                    #去除单位
                    for unit in unitLst:
                        if unit in value:
                            value = value[:value.find(unit)]
                            break
                    tempLst.append(value)
                else:
                    tempLst.append('')

            testResult =  GetValueFromDic(dataDic[keyName],'test_result','FAIL')
            if 'TRUE'== testResult:
                dataDic[keyName]['test_result'] = 'TRUE'
            else:
                dataDic[keyName]['test_result'] = testResult
                filemt= time.localtime(os.stat(file).st_mtime)  
                strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
                errDiskLst.append([keyName,dataDic[keyName]['MMS_PC'],testResult,strTime])
                if testResult != '' and testResult != 'PASS' and testResult != 'TRUE':
                    if caseName == 'AT_H2':
                        AppendErrDiskInfo('H2_Err',keyName,testResult,config['HWCONFIG']['MMS_PC'],file)
                    elif caseName == 'AT_CopyFile':
                        AppendErrDiskInfo('Copy File_Err',keyName,testResult,config['HWCONFIG']['MMS_PC'],file)

            #imageSuffix为空不需要截图，只需要数据
            if '' != imageSuffix:
                pos = file.rfind('\\report')
                imagepath = file[:pos]
                image = ''
                for i in os.listdir(imagepath):
                    if i.endswith('.bmp'):
                        image = os.path.join(imagepath,i)
                        break
                if os.path.isfile(image):
                    tempLst.append(image)
                else:
                    tempLst.append('')
            dataDic[keyName][caseKey] = tempLst


def ReadMarsIniDataEx(curpath, pattern, dataDic, caseName, recordCnt = 2):
    unitLst = ['M/s']
    for file in fileLst:
        if not re.match(pattern, file):
            continue
        #必须清除
        config.clear()
        config.read(file,encoding='gbk')
        if 'HWCONFIG' not in config.sections() or caseName not in config.sections():
            continue
        keyName = config['HWCONFIG']['mms_flash']
        if keyName not in dataDic:
            #0表示样片数量不确定，需要获取全部数据
            if recordCnt == len(dataDic) and 0 != recordCnt:
                continue
            dataDic[keyName] = {}
            dataDic[keyName]['capacity'] = config['HWCONFIG']['capacity']

            dataDic[keyName]['MMS_PC'] = config['HWCONFIG']['MMS_PC']

            fileMdTime = os.path.getmtime(file)
            dataDic[keyName][GetTimeKeyName(caseName)] = fileMdTime

            for key in config[caseName]:
                value = config[caseName][key]
                #去除单位
                for unit in unitLst:
                    if unit in value:
                        value = value[:value.find(unit)]
                        break
                dataDic[keyName][key] = value

            resultStr = GetValueFromDic(dataDic[keyName],'test_result')
            if 'TRUE'== resultStr:
                dataDic[keyName]['test_result'] = 'TRUE'
            elif 'unfinished' == resultStr:
                dataDic[keyName]['test_result'] = 'UNFINISHED' #测试中的情况单列，既不能作为测试通过，也不能作为测试fail，保留原样。
            else:
                dataDic[keyName]['test_result'] = resultStr
                filemt= time.localtime(os.stat(file).st_mtime)  
                strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
                errDiskLst.append([keyName,dataDic[keyName]['MMS_PC'],resultStr,strTime])
            dataDic[keyName]['file_path'] = file
        else:
            #重复样片，取最新的
            oldTime = dataDic[keyName][GetTimeKeyName(caseName)]
            fileMdTime = os.path.getmtime(file)
            if fileMdTime < oldTime:
                continue#数据不是新的，不做读取覆盖
            
            dataDic[keyName][GetTimeKeyName(caseName)] = fileMdTime

            #dataDic[keyName] = {}
            dataDic[keyName]['capacity'] = config['HWCONFIG']['capacity']

            dataDic[keyName]['MMS_PC'] = config['HWCONFIG']['MMS_PC']


            for key in config[caseName]:
                value = config[caseName][key]
                #去除单位
                for unit in unitLst:
                    if unit in value:
                        value = value[:value.find(unit)]
                        break
                dataDic[keyName][key] = value

            resultStr = GetValueFromDic(dataDic[keyName],'test_result')
            if 'TRUE'== resultStr:
                dataDic[keyName]['test_result'] = 'TRUE'
            elif 'unfinished' == resultStr:
                dataDic[keyName]['test_result'] = 'UNFINISHED' #测试中的情况单列，既不能作为测试通过，也不能作为测试fail，保留原样。
            else:
                dataDic[keyName]['test_result'] = resultStr
                filemt= time.localtime(os.stat(file).st_mtime)  
                strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
                errDiskLst.append([keyName,dataDic[keyName]['MMS_PC'],resultStr,strTime])
            dataDic[keyName]['file_path'] = file

def convert_to_number(value):
    """
    将变量转换为数值：
    - 若为字符串且以 0x 开头（不区分大小写），转为十六进制整数。
    - 否则尝试转为整数或浮点数。
    """
    if isinstance(value, str):
        stripped = value.strip().lower()  # 去空格并统一为小写
        if stripped.startswith("0x"):
            return int(stripped[2:], 16)      # 十六进制转整数
        else:
            # 尝试转为整数或浮点数
            try:
                return int(stripped)
            except ValueError:
                return float(stripped)     # 若含小数点则返回浮点数
    else:
        # 非字符串直接转为数值（如 int/float 类型）
        return float(value) if isinstance(value, float) else int(value)

def ReadMarsIDEIniData(curpath, pattern, dataDic, caseName, recordCnt = 2):
    unitLst = ['M/s']
    err_flag = '[QA-Error]'
    err_info = ''
    for file in fileLst:
        if not re.match(pattern, file):
            continue
        # 必须清除
        config.clear()
        config.read(file, encoding='gbk')
        if 'HWCONFIG' not in config.sections() or caseName not in config.sections():
            continue
        keyName = config['HWCONFIG']['mms_flash']
        if keyName not in dataDic:
            # 0表示样片数量不确定，需要获取全部数据
            if recordCnt == len(dataDic) and 0 != recordCnt:
                continue
            dataDic[keyName] = {}

            dataDic[keyName]['capacity'] = config['HWCONFIG']['capacity']
            dataDic[keyName]['MMS_PC'] = config['HWCONFIG']['MMS_PC']
            fileMdTime = os.path.getmtime(file)
            dataDic[keyName][GetTimeKeyName(caseName)] = fileMdTime

            for key in config[caseName]:
                value = config[caseName][key]
                # 去除单位
                for unit in unitLst:
                    if unit in value:
                        value = value[:value.find(unit)]
                        break
                dataDic[keyName][key] = value

            resultStr = GetValueFromDic(dataDic[keyName], 'test_result')
            if err_flag in resultStr:
                err_info = resultStr[resultStr.find(err_flag) + len(err_flag)+1:]
            HgError = '高等级错误'
            for i in range(11):
                HgErrorInfo = HgError + str(i+1)
                if GetErrorInfo(dataDic[keyName], HgErrorInfo) == '':
                    dataDic[keyName][HgErrorInfo] = err_info
                    for j in range(i+1,12):
                        dataDic[keyName][HgError + str(j+1)] = ''
                    break
                else:
                    dataDic[keyName][HgErrorInfo] = GetErrorInfo(dataDic[keyName], HgErrorInfo)
            dataDic[keyName]['file_path'] = file
        else:
            # 重复样片，取最新的
            oldTime = dataDic[keyName][GetTimeKeyName(caseName)]
            fileMdTime = os.path.getmtime(file)
            if fileMdTime < oldTime:
                continue  # 数据不是新的，不做读取覆盖

            dataDic[keyName]['MMS_PC'] = config['HWCONFIG']['MMS_PC']
            dataDic[keyName][GetTimeKeyName(caseName)] = fileMdTime

            for key in config[caseName]:
                value = config[caseName][key]
                # 去除单位
                for unit in unitLst:
                    if unit in value:
                        value = value[:value.find(unit)]
                        break
                dataDic[keyName][key] = value

            resultStr = GetValueFromDic(dataDic[keyName], 'test_result')
            if err_flag in resultStr:
                err_info = resultStr[resultStr.find(err_flag) + len(err_flag):]
            HgError = '高等级错误'
            for i in range(11):
                HgErrorInfo = HgError + str(i + 1)
                if GetErrorInfo(dataDic[keyName], HgErrorInfo) == '':
                    dataDic[keyName][HgErrorInfo] = err_info
                    for j in range(i + 1, 12):
                        dataDic[keyName][HgError + str(j + 1)] = ''
                    break
                else:
                    dataDic[keyName][HgErrorInfo] = GetErrorInfo(dataDic[keyName], HgErrorInfo)
            dataDic[keyName]['file_path'] = file

def GetErrorInfo(dataDic, key, defaultVulue = ''):
    if key in dataDic:
        return dataDic[key]
    else:
        return defaultVulue

def WriteDataAndImage(worksheet, startLine, imageLine, dataDic, caseKey, colLst, keyLst, imgWidth = 260, imgHeight = 240, startCol = 2, colCnt = 3):
    curLine = startLine
    imageCol = startCol
    keySortLst = sorted(dataDic.keys(), reverse=False)
    for key in keySortLst:
        if caseKey not in dataDic[key]:
            curLine += 1
            continue
        line = dataDic[key][caseKey]
        for index,col in enumerate(colLst):
            if 0 == index:
                keyNo = key
                if  'Cap' == keyLst[0]:
                    keyNo = '%s-%sG'%(key, line[0])
                worksheet['%s%d'%(col, curLine)] = keyNo
            else:
                worksheet['%s%d'%(col, curLine)] = line[index]
        # 列表最后一项是图片路径
        if '' != line[-1]:
            img = Image(line[-1])
            img.width = imgWidth
            img.height = imgHeight
            worksheet.add_image(img, '%s%d'%(get_column_letter(imageCol), imageLine))
        curLine += 1
        imageCol += colCnt

#1个样片多条记录
def WriteData(worksheet, startLine, dataDic, colLst, keyLst, lineCnt = 1,resultColNameList = []):
    curLine = startLine
    for key in dataDic:
        for line in dataDic[key]['data']:
            for index,col in enumerate(colLst):
                try:
                    if 0 == index:
                        #第一列是编号，直接填key
                        worksheet['%s%d'%(col, curLine)] = key
                    else:
                        worksheet['%s%d'%(col, curLine)] = line[index-1]
                        if 'A5-A6' == keyLst[index-1] and line[index-1] != '':
                            if line[index-1] >= 400:
                                worksheet['%s%d'%(col, curLine)].fill = warnFill
                        if col in resultColNameList:
                            strResult = line[index-1].upper()
                            if strResult != 'TRUE' and strResult != 'PASS' and strResult != '':
                                worksheet['%s%d'%(col, curLine)].fill = warnFill
                    worksheet['%s%d'%(col, curLine)].alignment = alignment
                #合并的单元格只能写一次，需要捕获异常
                except(AttributeError):
                    continue
            curLine += lineCnt
            startLine += 1
    return startLine
#1个样片只有一条记录  lineCnt兼容excel多行合并成一行的情况
def WriteDataNormal(worksheet, startLine, dataDic, colLst, keyLst, lineCnt = 1,resultColNameList = []):
    curLine = startLine
    for key in dataDic:
        line = dataDic[key]
        for index,col in enumerate(colLst):
            try:
                if 0 == index:
                    #第一列是编号，直接填key
                    worksheet['%s%d'%(col, curLine)] = key
                else:
                    worksheet['%s%d'%(col, curLine)] = line[index-1]
                    if 'A5-A6' == keyLst[index-1]:
                        if line[index-1] >= 400:
                            worksheet['%s%d'%(col, curLine)].fill = warnFill
                    if col in resultColNameList:
                        strResult = line[index-1].upper()
                        if strResult == 'UNFINISHED':
                            #填写独特的颜色
                            worksheet['%s%d'%(col, curLine)].fill = unfinishedFill
                        elif strResult != 'TRUE' and strResult != 'PASS' and strResult != '':
                            worksheet['%s%d'%(col, curLine)].fill = warnFill
                worksheet['%s%d'%(col, curLine)].alignment = alignment
            #合并的单元格只能写一次，需要捕获异常
            except(AttributeError):
                continue
        curLine += lineCnt

#获取每个编号每列数据的最值
def GetMaxOrMinValueLst(keyLst,dataDic):
    for key in dataDic:
        resultLst = []
        for index,col in enumerate(keyLst):
            tempLst = [line[index] for line in dataDic[key]]
            limitData = 0
            bFirstData = True
            for data in tempLst:
                try:
                    tempData = float(data)
                    #部分列需要取最小值，例如时间等
                    if bFirstData:
                        limitData = tempData
                        bFirstData = False
                        continue
                    if col in minKey:
                        if tempData < limitData:
                            limitData = tempData
                    else:
                        if tempData > limitData:
                            limitData = tempData
                except:
                    continue
            resultLst.append(limitData)
        dataDic[key].append(resultLst)

def FmtStrHex(strHex):
    #去掉十六进制前面的多个0
    strNew = strHex.lstrip('0')
    if '' == strNew:
        strNew = '0'
    return strNew

def GetImtResultPath(strImtBmpPath, key):
    pos = strImtBmpPath.rfind('\\')
    strPath = strImtBmpPath[:pos+1] + 'inst%s_IometerResults.csv'%key
    return strPath

def GetNewIoMeterDic(oldDic, startPos, smartKey, bA23 = False):
    #startPos之后的数据为smart信息，需转换为模板所需数据[F1(G),F2(G),smart(非0),A5-A6]
    newDic = {}
    for key in oldDic:
        newDic[key] = []
        for dataLst in oldDic[key]:
            newLst = dataLst[:startPos]
            smartLst = dataLst[startPos:]
            #F1、F2 1个单位为32M，需转化为G
            if smartLst[0] == '':
                newLst.append('')
            else:
                newLst.append((int(smartLst[0],16)*32)//1024)
            if smartLst[1] == '':
                newLst.append('')
            else:
                newLst.append((int(smartLst[1],16)*32)//1024)
            strSmart = ''
            for idx,value in enumerate(smartLst[4:]):
                if bA23 and idx == len(smartLst[4:])-1:
                    break
                if value != '' and 0 != int(value,16):
                    strSmart += '%s=%s,'%(smartKey[4+idx], FmtStrHex(value))
            if '' != strSmart:
                strSmart = strSmart[:-1]
            newLst.append(strSmart)
            if smartLst[2] == '' or smartLst[3] == '':
                newLst.append('')
            else:
                newLst.append(int(smartLst[2],16)-int(smartLst[3],16))
            if bA23:
                strImtResultPath = GetImtResultPath(smartLst[-1], key)
                newLst.append(strImtResultPath)
            newDic[key].append(newLst)
    return newDic

def GetNewBitDic(oldDic, startPos, smartKey):
    #startPos之后的数据为smart信息，需转换为模板所需数据[F1(G)/F2(G),smart(非0),A5-A6]
    newDic = {}
    for key in oldDic:
        newDic[key] = []
        for dataLst in oldDic[key]:
            newLst = dataLst[:startPos]
            smartLst = dataLst[startPos:]
            #F1、F2 1个单位为32M，需转化为G
            write = 0
            if smartLst[0] != '':
                write = (int(smartLst[0],16)*32)//1024
            read = 0
            if smartLst[1] != '':
                read = (int(smartLst[1],16)*32)//1024
            newLst.append('%d/%d'%(write,read))
            strSmart = ''
            for idx,value in enumerate(smartLst[4:]):
                if value != '' and 0 != int(value,16):
                    strSmart += '%s=%s,'%(smartKey[4+idx], FmtStrHex(value))
            if '' != strSmart:
                strSmart = strSmart[:-1]
            newLst.append(strSmart)
            if smartLst[2] == '' or smartLst[3] == '':
                newLst.append('')
            else:
                newLst.append(int(smartLst[2],16)-int(smartLst[3],16))
            newDic[key].append(newLst)
    return newDic

def ReadQaIniDataForNano(curpath, pattern, dataDic, caseKey, keyLst, imageSuffix, dicMax, caseName, diskCnt = 6):
    unitLst = ['MByte/s', 'MB/s', 'ms', '%']
    for file in fileLst:
        if not re.match(pattern, file):
            continue
        pos = file.rfind('\\')
        imagepath = file[:pos]
        #必须清除
        config.clear()
        config.read(file,encoding = 'gbk')
        for sec in config.sections():
            if sec not in dataDic:
                #diskCnt为0表示统计所有磁盘数据
                if diskCnt == len(dataDic) and 0 != diskCnt:
                    continue
                dataDic[sec] = {}
            if caseKey not in dataDic[sec]:
                tempLst = []
                pcNo = ''
                if 'pc_no' in config[sec]:
                    pcNo = config[sec]['pc_no']
                for key in keyLst:
                    if key.lower() in config[sec]:
                        value = config[sec][key.lower()]
                        #去除单位
                        for unit in unitLst:
                            if unit in value:
                                value = value[:value.find(unit)]
                                break
                        tempLst.append(value)
                        if 'qa_err_msg' == key:
                            filemt= time.localtime(os.stat(file).st_mtime)  
                            strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
                            errDiskLst.append([sec,pcNo,value,strTime])
                        if '' != value:
                            fValue = float(value)
                            # 区分hdtune读写相同字段，避免最值比较混乱
                            maxKey = '%s_%s'%(caseName, key)
                            if maxKey not in dicMax or fValue > dicMax[maxKey]:
                                dicMax[maxKey] = fValue
                    else:
                        tempLst.append('')
                #imageSuffix为空不需要截图，只需要数据
                if '' != imageSuffix:
                    image = imagepath+'\\%s_%s'%(sec, imageSuffix)
                    if os.path.isfile(image):
                        tempLst.append(image)
                    else:
                        tempLst.append('')
                dataDic[sec][caseKey] = tempLst

def WriteDataAndImageForNano(worksheet, wsPic, startLine, dataDic, itemLst, startColLst, keyLst, dicMax, caseLst, picColLst):
    imgWidth = 240
    imgHeight = 190
    curLine = startLine
    keySortLst = sorted(dataDic.keys(), reverse=False)
    for key in keySortLst:
        worksheet['F%d'%curLine] = key
        wsPic['F%d'%curLine] = key
        for itemIdx,itLst in enumerate(itemLst):
            for itIdx,itLst in enumerate(itLst):
                itemLine = curLine
                startCol = startColLst[itemIdx][itIdx]
                for item in itLst:
                    curCol = column_index_from_string(startCol)
                    if item not in dataDic[key]:
                        itemLine += 1
                        continue
                    for idx,data in enumerate(dataDic[key][item][:-1]):
                        worksheet['%s%d'%(get_column_letter(curCol), itemLine)] = data
                        maxKey = '%s_%s'%(caseLst[itemIdx], keyLst[itemIdx][idx])
                        if '' != data and maxKey in dicMax:
                            fData = float(data)
                            if fData == dicMax[maxKey]:
                                worksheet['%s%d'%(get_column_letter(curCol), itemLine)].fill = maxFill
                            elif fData <= dicMax[maxKey]*0.8:
                                worksheet['%s%d'%(get_column_letter(curCol), itemLine)].fill = warnFill
                        curCol+=1
                    # 列表最后一项是图片路径
                    picPath = dataDic[key][item][-1]
                    if '' != picPath:
                        img = Image(picPath)
                        img.width = imgWidth
                        img.height = imgHeight
                        wsPic.add_image(img, '%s%d'%(picColLst[itemIdx][itIdx], itemLine))
                    itemLine += 1
        curLine += 6


#写时间信息
def WriteReportTime(worksheet,columnName,rowNo):
    #capIdx = 0
    filemt= time.localtime()  
    strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
    worksheet['%s%d'%(columnName, rowNo)] = strTime

#写操作者信息
def WriteReportOperator(worksheet,columnName,rowNo,operatorName = 'Skynet'):
    #capIdx = 0
    worksheet['%s%d'%(columnName, rowNo)] = operatorName

def GetValueFromDic(dataDic, key,defaultVulue = ''):
    if key in dataDic:
        return dataDic[key]
    else:
        return defaultVulue


#获取测试时间
def GetTestTimeStrFromDic(dic):
    #测试时间
    endTimeStr = GetValueFromDic(dic,'end_time')
    startTimeStr = GetValueFromDic(dic,'start_time')
    if '' == endTimeStr or '' == startTimeStr:
        return ''
    else:
        try:
            endtime = datetime.strptime(endTimeStr, '%Y-%m-%d %H:%M:%S')
            starttime = datetime.strptime(startTimeStr, '%Y-%m-%d %H:%M:%S')
            totalSecond = timedelta.total_seconds(endtime-starttime)
            totalSecond = int(totalSecond)
            hour = int(totalSecond/3600)
            lefSeconds = totalSecond%3600
            minutes = int(lefSeconds/60)
            seconds = lefSeconds%60
            timeStr = '%d:%d:%d'%(hour,minutes,seconds)
            return timeStr
        except:
            return ''

#获取测试时间
def GetTestTimeValueFromDic(dic):
    #测试时间
    endTimeStr = GetValueFromDic(dic,'end_time')
    startTimeStr = GetValueFromDic(dic,'start_time')
    if '' == endTimeStr or '' == startTimeStr:
        return 0
    else:
        try:
            endtime = datetime.strptime(endTimeStr, '%Y-%m-%d %H:%M:%S')
            starttime = datetime.strptime(startTimeStr, '%Y-%m-%d %H:%M:%S')
            totalSecond = timedelta.total_seconds(endtime-starttime)
            totalSecond = int(totalSecond)
            return totalSecond
        except:
            return 0

def GetIndex(element,elementlist):
    elementIdx = -1
    try:
        elementIdx = elementlist.index(element)
    except:
        elementIdx = -1
    return elementIdx

def Safe_float(rawData):
    newData = ''
    try:
        newData = float(rawData)
    except:
        newData = ''
    return newData

def AppendErrDiskInfo(errTypeName,sampleID,errCode,pcNo,logFilePath):
    errDiskInfo = []
    errDiskInfo.append(sampleID)
    errDiskInfo.append(errCode)
    errDiskInfo.append(pcNo)
    errDiskInfo.append(logFilePath)
    if IsInErrDiskSet(errTypeName,sampleID) == False:
        ErrDiskInfo.g_dicErrDisk[errTypeName].append(errDiskInfo)
    else:
        #比较时间，将最新记录的数据传上去
        idx = GetErrDiskInfoIdx(errTypeName,sampleID)
        if idx != -1:
            orgErrInfo = ErrDiskInfo.g_dicErrDisk[errTypeName][idx]
            oldTime = os.path.getmtime(orgErrInfo[-1])
            curTime = os.path.getmtime(logFilePath)
            if curTime > oldTime:
                orgErrInfo[1] = errCode
                orgErrInfo[2] = pcNo
                orgErrInfo[3] = logFilePath

#判定此样本编号是否已经在错误列表中
def IsInErrDiskSet(errTypeName,sampleID):
    for errDisk in ErrDiskInfo.g_dicErrDisk[errTypeName]:
        sampleNo = errDisk[0]
        if sampleNo == sampleID:
            return True
    return False

#判定此样本编号是否已经在错误列表中
def GetErrDiskInfoIdx(errTypeName,sampleID):
    idx = 0
    for errDisk in ErrDiskInfo.g_dicErrDisk[errTypeName]:
        sampleNo = errDisk[0]
        if sampleNo == sampleID:
            return idx
        idx += 1
    return -1

#获取样本编号的值
def GetOrderNoFromSampleNo(_sampleNo = 'AI-JGS-001'):
    if '' == _sampleNo:
        nOrderNo = -1 #无效编号
        strSample = ''
    else:
        strOrder = _sampleNo[-3:]
        strSample = _sampleNo[:-3]
        nOrderNo = int(strOrder)
    return nOrderNo, strSample

def GetTimeKeyName(caseKey):
    timeKeyName = caseKey + '_file_time'
    return timeKeyName

#获取当前日期
def GetDate():
    #capIdx = 0
    filemt= time.localtime()  
    strTime = time.strftime('%Y-%m-%d', filemt)
    return strTime

def JustOpenCloseExcelFile(filename):
    try:
        xlApp = Dispatch("Excel.Application")
        xlApp.Visible = False
        xlBook = xlApp.Workbooks.Open(filename)
        xlBook.Save()
        xlBook.Close(SaveChanges=True)
        xlApp.Quit()
        del xlApp
        return True
    except:
        try:
            xlBook.Close(SaveChanges=True)
            xlApp.Quit()
            del xlApp
            return False
        except:
            xlApp.Quit()
            del xlApp
            return False

def TryRepeatOpenCloseExcelFile(filename):
    for i in range(1,20):
        if JustOpenCloseExcelFile(filename):
            break
        time.sleep(5)
        
def modfiyPathName(dirPathName):
    if '\\?\\UNC' in dirPathName:
        digitMatch = re.search(r'\d', dirPathName)
        if digitMatch:
            dirPathName = '\\\\' + dirPathName[digitMatch.start():]
    return dirPathName

#virtualPlan 虚拟Plan，用于需要将多个Plan的数据装进同一个Plan的情况，所以需要改变他们本来所属的Plan
def ReadMarsIniData_New(gDataDic,xlsSheetName,xlsTableName,xlsCaseName,marsCaseName,dataItemsListName,smartItemsListName,curpath, pattern, virtualPlan = '', imageSuffix = '', diskCnt = 0):
    dataKeyLst = manager.get_list(dataItemsListName)
    smartKeyLst = manager.get_list(smartItemsListName)
    unitLst = ['M/s']
    config = configparser.RawConfigParser()
    strPlan = virtualPlan
    planDataDic = {}
    for file in fileLst:
        if not re.match(pattern, file):
            continue
        #必须清除
        config.clear()
        config.read(file,encoding = 'gbk')
        if 'HWCONFIG' not in config.sections():
            continue
        sampleNo = config['HWCONFIG']['mms_flash']

        if xlsSheetName not in gDataDic:
            gDataDic[xlsSheetName] = {}

        if xlsTableName not in gDataDic[xlsSheetName]:
            gDataDic[xlsSheetName][xlsTableName] = {}
        
        if strPlan == '':
            strPlan = GetPlan(file)

        if strPlan == '':
            continue

        if strPlan not in gDataDic[xlsSheetName][xlsTableName]:
            gDataDic[xlsSheetName][xlsTableName][strPlan] = {}

        planDataDic = gDataDic[xlsSheetName][xlsTableName][strPlan]

        if sampleNo not in planDataDic:
            #0表示样片数量不确定，需要获取全部数据
            if diskCnt == len(planDataDic) and 0 != diskCnt:
                continue
            planDataDic[sampleNo] = {}
        if marsCaseName not in config.sections():
            continue
        #一个样片的全局信息只获取一次。

        sampleDic = planDataDic[sampleNo]
        if 'g_pc_no' not in sampleDic or sampleDic['g_pc_no'] == '':
            sampleDic['g_pc_no'] = config['HWCONFIG']['MMS_PC']
        if 'capacity' not in sampleDic or sampleDic['capacity'] == '':
            cap = config['HWCONFIG']['capacity']
            if '' == cap:
                sampleDic['capacity']=''
            else:
                sampleDic['capacity']=str(int(float(cap)))
   
        if xlsCaseName not in sampleDic:
            sampleDic[xlsCaseName] = {}
            caseDic = sampleDic[xlsCaseName]
            fileMdTime = os.path.getmtime(file)
            caseDic['m_time'] = fileMdTime
            caseDic['pc_no'] = config['HWCONFIG']['MMS_PC']
            caseDic['file'] = file
            caseDic['data'] = {}
            caseDic['data_items_list'] = dataItemsListName
            
            #if 'test_result' in config[caseName]:
            #    planDataDic[sampleNo]['test_result'] = config[caseName]['test_result']

            #读取纯数据
            dataDic = {}
            for key in dataKeyLst:
                if key.lower() == 'pc_no':
                    dataDic[key] = caseDic['pc_no']
                if key.lower() in config[marsCaseName]:
                    value = config[marsCaseName][key.lower()]
                    #去除单位
                    for unit in unitLst:
                        if unit in value:
                            value = value[:value.find(unit)]
                            break
                    dataDic[key] = value
                elif 'test_result' == key.lower():
                    dataDic[key] = value
                elif 'test_time' == key.lower():
                    #测试时间
                    strTime = GetMarsTestTime(config[marsCaseName])
                    dataDic[key] = strTime
                
            caseDic['data'] = dataDic

            #读取Smart数据
            smartDic = {}
            smartDic = ReadSmart(config,marsCaseName,smartKeyLst)
                
            caseDic['smart'] = smartDic
            caseDic['smart_items_list'] = smartItemsListName


            resultStr = GetValueFromDic(dataDic,'test_result')
            if 'TRUE'== resultStr:
                planDataDic[sampleNo]['test_result'] = 'TRUE'
            elif 'unfinished' == resultStr:
                planDataDic[sampleNo]['test_result'] = 'UNFINISHED' #测试中的情况单列，既不能作为测试通过，也不能作为测试fail，保留原样。
            else:
                planDataDic[sampleNo]['test_result'] = resultStr

            #imageSuffix为空不需要截图，只需要数据
            caseDic['image'] = ''
            if '' != imageSuffix:
                pos = file.rfind('\\report')
                imagepath = file[:pos]
                image = ''
                for i in os.listdir(imagepath):
                    if i.endswith('.bmp'):
                        image = os.path.join(imagepath,i)
                        break
                if os.path.isfile(image):
                    caseDic['image'] = image
                
        else:
            #如果样片已经存在，需要检查是否是新数据，新数据才覆盖
            caseDic = sampleDic[xlsCaseName]
            oldTime = caseDic['m_time']
            fileMdTime = os.path.getmtime(file)
            if fileMdTime < oldTime:
                continue#数据不是新的，不做读取覆盖

            caseDic['m_time'] = fileMdTime
            caseDic['pc_no'] = config['HWCONFIG']['MMS_PC']
            caseDic['file'] = file
            caseDic['data'] = {}
            
            #if 'test_result' in config[caseName]:
            #    planDataDic[sampleNo]['test_result'] = config[caseName]['test_result']

            #读取纯数据
            dataDic = {}
            for key in dataKeyLst:
                if key.lower() == 'pc_no':
                    dataDic[key] = caseDic['pc_no']
                if key.lower() in config[marsCaseName]:
                    value = config[marsCaseName][key.lower()]
                    #去除单位
                    for unit in unitLst:
                        if unit in value:
                            value = value[:value.find(unit)]
                            break
                    dataDic[key] = value
                elif 'test_result' == key.lower():
                    dataDic[key] = value
                elif 'test_time' == key.lower():
                    #测试时间
                    strTime = GetMarsTestTime(config[marsCaseName])
                    dataDic[key] = strTime
               
            caseDic['data'] = dataDic

            #读取Smart数据
            smartDic = {}
            smartDic = ReadSmart(config,marsCaseName,smartKeyLst)               
            caseDic['smart'] = smartDic


            resultStr = GetValueFromDic(dataDic,'test_result')
            if 'TRUE'== resultStr:
                planDataDic[sampleNo]['test_result'] = 'TRUE'
            elif 'unfinished' == resultStr:
                planDataDic[sampleNo]['test_result'] = 'UNFINISHED' #测试中的情况单列，既不能作为测试通过，也不能作为测试fail，保留原样。
            else:
                planDataDic[sampleNo]['test_result'] = resultStr

            #imageSuffix为空不需要截图，只需要数据
            caseDic['image'] = ''
            if '' != imageSuffix:
                pos = file.rfind('\\report')
                imagepath = file[:pos]
                image = ''
                for i in os.listdir(imagepath):
                    if i.endswith('.bmp'):
                        image = os.path.join(imagepath,i)
                        break
                if os.path.isfile(image):
                    caseDic['image'] = image
         

            #获取Case名称-------------------------------------------------------------

def ReadQaIniData_New(gDataDic, xlsSheetName, xlsTableName, xlsCaseName, dataItemsListName, curpath, pattern, imageSuffix='', diskCnt=0):
    """
    从INI文件中读取多个样片的QA测试数据

    参数:
        gDataDic: 全局数据字典
        xlsSheetName: Excel工作表名称
        xlsTableName: Excel表格名称
        xlsCaseName: Excel列名称
        dataItemsListName: 数据项列表名称
        curpath: 当前路径
        pattern: 文件匹配模式
        imageSuffix: 图像后缀
        diskCnt: 磁盘数量限制
    """
    dataKeyLst = manager.get_list(dataItemsListName)
    unitLst = ['MByte/s', 'MB/s', 'ms', '%']
    config = configparser.RawConfigParser()
    strPlan = ''
    planDataDic = {}

    for file in fileLst:
        if not re.match(pattern, file):
            continue

        pos = file.rfind('\\')
        imagepath = file[:pos]

        # 必须清除
        config.clear()
        config.read(file, encoding='gbk')

        # 获取Plan信息
        if strPlan == '':
            strPlan = GetPlan(file)

        if strPlan == '':
            continue

        # 初始化数据结构
        if xlsSheetName not in gDataDic:
            gDataDic[xlsSheetName] = {}

        if xlsTableName not in gDataDic[xlsSheetName]:
            gDataDic[xlsSheetName][xlsTableName] = {}

        if strPlan not in gDataDic[xlsSheetName][xlsTableName]:
            gDataDic[xlsSheetName][xlsTableName][strPlan] = {}

        planDataDic = gDataDic[xlsSheetName][xlsTableName][strPlan]

        # 遍历INI文件中的所有样片section
        for sec in config.sections():
            # 跳过HWCONFIG等非样片section
            if sec == 'HWCONFIG' or sec.startswith('WorkOrder'):
                continue

            # 检查磁盘数量限制
            if sec not in planDataDic:
                if diskCnt == len(planDataDic) and 0 != diskCnt:
                    continue
                planDataDic[sec] = {}

            sampleDic = planDataDic[sec]

            # 获取样片的全局信息（只获取一次）
            if 'g_pc_no' not in sampleDic or sampleDic['g_pc_no'] == '':
                if 'pc_no' in config[sec]:
                    sampleDic['g_pc_no'] = config[sec]['pc_no']
                else:
                    sampleDic['g_pc_no'] = ''

            if 'capacity' not in sampleDic or sampleDic['capacity'] == '':
                if 'cap' in config[sec]:
                    cap = config[sec]['cap']
                    if '' == cap:
                        sampleDic['capacity'] = ''
                    else:
                        try:
                            sampleDic['capacity'] = str(int(float(cap)))
                        except:
                            sampleDic['capacity'] = cap
                elif 'cap_mb' in config[sec]:
                    cap_mb = config[sec]['cap_mb']
                    if '' == cap_mb:
                        sampleDic['capacity'] = ''
                    else:
                        try:
                            # 将MB转换为GB
                            sampleDic['capacity'] = str(int(float(cap_mb) / 1024))
                        except:
                            sampleDic['capacity'] = cap_mb
                else:
                    sampleDic['capacity'] = ''

            # 处理测试用例数据
            if xlsCaseName not in sampleDic:
                sampleDic[xlsCaseName] = {}
                caseDic = sampleDic[xlsCaseName]
                fileMdTime = os.path.getmtime(file)
                caseDic['m_time'] = fileMdTime
                caseDic['pc_no'] = sampleDic['g_pc_no']
                caseDic['file'] = file
                caseDic['data'] = {}
                caseDic['data_items_list'] = dataItemsListName

                # 读取数据项
                dataDic = {}
                for key in dataKeyLst:
                    if key.lower() in config[sec]:
                        value = config[sec][key.lower()]
                        # 去除单位
                        for unit in unitLst:
                            if unit in value:
                                value = value[:value.find(unit)]
                                break
                        dataDic[key] = value
                    else:
                        dataDic[key] = ''

                if 'qa_err_msg' in dataDic:
                    errCode = dataDic['qa_err_msg'].upper()
                    if errCode == '' or errCode == 'PASS' or errCode == 'TRUE':
                        dataDic['qa_err_msg'] = 'TRUE'

                caseDic['data'] = dataDic

                # 处理图像文件
                if '' != imageSuffix:
                    image = imagepath + '\\%s_%s' % (sec, imageSuffix)
                    if os.path.isfile(image):
                        caseDic['image'] = image
                    else:
                        caseDic['image'] = ''

                # 处理测试结果
                # if 'qa_err_msg' in dataDic:
                #     errCode = dataDic['qa_err_msg'].upper()
                #     if errCode == '' or errCode == 'PASS' or errCode == 'TRUE':
                #         sampleDic['test_result'] = 'TRUE'
                #     elif errCode == 'UNFINISHED':
                #         sampleDic['test_result'] = 'UNFINISHED'
                #     else:
                #         sampleDic['test_result'] = errCode
                #         # 记录错误信息
                #         filemt = time.localtime(os.stat(file).st_mtime)
                #         strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
                #         errDiskLst.append([sec, sampleDic['g_pc_no'], errCode, strTime])
                #         AppendErrDiskInfo('QA_Err', sec, errCode, sampleDic['g_pc_no'], file)
                # else:
                #     sampleDic['test_result'] = ''

            else:
                # 如果样片已经存在，需要检查是否是新数据
                caseDic = sampleDic[xlsCaseName]
                oldTime = caseDic['m_time']
                fileMdTime = os.path.getmtime(file)
                if fileMdTime < oldTime:
                    continue  # 数据不是新的，不做读取覆盖

                caseDic['m_time'] = fileMdTime
                caseDic['pc_no'] = sampleDic['g_pc_no']
                caseDic['file'] = file
                caseDic['data'] = {}

                # 读取数据项
                dataDic = {}
                for key in dataKeyLst:
                    if key.lower() in config[sec]:
                        value = config[sec][key.lower()]
                        # 去除单位
                        for unit in unitLst:
                            if unit in value:
                                value = value[:value.find(unit)]
                                break
                        dataDic[key] = value
                    else:
                        dataDic[key] = ''

                if 'qa_err_msg' in dataDic:
                    errCode = dataDic['qa_err_msg'].upper()
                    if errCode == '' or errCode == 'PASS' or errCode == 'TRUE':
                        dataDic['qa_err_msg'] = 'TRUE'

                caseDic['data'] = dataDic

                # 处理图像文件
                if '' != imageSuffix:
                    image = imagepath + '\\%s_%s' % (sec, imageSuffix)
                    if os.path.isfile(image):
                        caseDic['image'] = image
                    else:
                        caseDic['image'] = ''


def ReadFioIniData_New(gDataDic, xlsSheetName, xlsTableName, xlsCaseName, dataItemsListName, curpath, pattern, imageSuffix='', diskCnt=0):
    """
    从INI文件中读取多个样片的Fio测试数据

    参数:
        gDataDic: 全局数据字典
        xlsSheetName: Excel工作表名称
        xlsTableName: Excel表格名称
        xlsCaseName: Excel列名称
        dataItemsListName: 数据项列表名称
        curpath: 当前路径
        pattern: 文件匹配模式
        imageSuffix: 图像后缀
        diskCnt: 磁盘数量限制
    """
    dataKeyLst = manager.get_list(dataItemsListName)
    unitLst = ['KB/s', 'MB/s']
    config = configparser.RawConfigParser()
    strPlan = ''
    planDataDic = {}

    for file in fileLst:
        if not re.match(pattern, file):
            continue

        pos = file.rfind('\\')
        imagepath = file[:pos]

        # 必须清除
        config.clear()
        config.read(file, encoding='gbk')

        # 获取Plan信息
        if strPlan == '':
            strPlan = GetPlan(file)

        if strPlan == '':
            continue

        # 初始化数据结构
        if xlsSheetName not in gDataDic:
            gDataDic[xlsSheetName] = {}

        if xlsTableName not in gDataDic[xlsSheetName]:
            gDataDic[xlsSheetName][xlsTableName] = {}

        if strPlan not in gDataDic[xlsSheetName][xlsTableName]:
            gDataDic[xlsSheetName][xlsTableName][strPlan] = {}

        planDataDic = gDataDic[xlsSheetName][xlsTableName][strPlan]

        # 遍历INI文件中的所有样片section
        for sec in config.sections():
            # 跳过HWCONFIG等非样片section
            if sec == 'HWCONFIG' or sec.startswith('WorkOrder'):
                continue

            # 检查磁盘数量限制
            if sec not in planDataDic:
                if diskCnt == len(planDataDic) and 0 != diskCnt:
                    continue
                planDataDic[sec] = {}

            sampleDic = planDataDic[sec]

            # 获取样片的全局信息（只获取一次）
            if 'g_pc_no' not in sampleDic or sampleDic['g_pc_no'] == '':
                if 'pc_no' in config[sec]:
                    sampleDic['g_pc_no'] = config[sec]['pc_no']
                else:
                    sampleDic['g_pc_no'] = ''

            if 'capacity' not in sampleDic or sampleDic['capacity'] == '':
                if 'cap' in config[sec]:
                    cap = config[sec]['cap']
                    if '' == cap:
                        sampleDic['capacity'] = ''
                    else:
                        try:
                            sampleDic['capacity'] = str(int(float(cap)))
                        except:
                            sampleDic['capacity'] = cap
                elif 'cap_mb' in config[sec]:
                    cap_mb = config[sec]['cap_mb']
                    if '' == cap_mb:
                        sampleDic['capacity'] = ''
                    else:
                        try:
                            # 将MB转换为GB
                            sampleDic['capacity'] = str(int(float(cap_mb) / 1024))
                        except:
                            sampleDic['capacity'] = cap_mb
                else:
                    sampleDic['capacity'] = ''

            # 处理测试用例数据
            if xlsCaseName not in sampleDic:
                sampleDic[xlsCaseName] = {}
                caseDic = sampleDic[xlsCaseName]
                fileMdTime = os.path.getmtime(file)
                caseDic['m_time'] = fileMdTime
                caseDic['pc_no'] = sampleDic['g_pc_no']
                caseDic['file'] = file
                caseDic['data'] = {}
                caseDic['data_items_list'] = dataItemsListName

                # 读取数据项
                #从同目录的fiotest.ini文件中读取数据项
                dataDic = {}
                fiotest = imagepath + '\\fiotest.ini'
                config.clear()
                if os.path.isfile(fiotest):
                    # 处理无section格式
                    if not config.sections():
                        config[sec] = {}
                        with open(fiotest, 'r', encoding='gbk') as f:
                            for line in f:
                                if '=' in line:
                                    key, value = line.strip().split('=', 1)
                                    config['DEFAULT'][key.strip()] = value.strip()

                    for key in dataKeyLst:
                        if key.lower() in config[sec]:
                            value = config[sec][key.lower()]
                            # 去除单位
                            for unit in unitLst:
                                if unit in value:
                                    value = value[:value.find(unit)]
                                    break
                            dataDic[key] = value
                        else:
                            dataDic[key] = ''
                            if key.lower() == 'test_result':
                                dataDic[key] = 'FAIL'

                if 'qa_err_msg' in dataDic:
                    errCode = dataDic['qa_err_msg'].upper()
                    if errCode == '' or errCode == 'PASS' or errCode == 'TRUE':
                        dataDic['qa_err_msg'] = 'TRUE'

                caseDic['data'] = dataDic

                # 处理图像文件
                if '' != imageSuffix:
                    image = imagepath + '\\%s_%s' % (sec, imageSuffix)
                    if os.path.isfile(image):
                        caseDic['image'] = image
                    else:
                        caseDic['image'] = ''

                # 处理测试结果
                # if 'qa_err_msg' in dataDic:
                #     errCode = dataDic['qa_err_msg'].upper()
                #     if errCode == '' or errCode == 'PASS' or errCode == 'TRUE':
                #         sampleDic['test_result'] = 'TRUE'
                #     elif errCode == 'UNFINISHED':
                #         sampleDic['test_result'] = 'UNFINISHED'
                #     else:
                #         sampleDic['test_result'] = errCode
                #         # 记录错误信息
                #         filemt = time.localtime(os.stat(file).st_mtime)
                #         strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
                #         errDiskLst.append([sec, sampleDic['g_pc_no'], errCode, strTime])
                #         AppendErrDiskInfo('QA_Err', sec, errCode, sampleDic['g_pc_no'], file)
                # else:
                #     sampleDic['test_result'] = ''

            else:
                # 如果样片已经存在，需要检查是否是新数据
                caseDic = sampleDic[xlsCaseName]
                oldTime = caseDic['m_time']
                fileMdTime = os.path.getmtime(file)
                if fileMdTime < oldTime:
                    continue  # 数据不是新的，不做读取覆盖

                caseDic['m_time'] = fileMdTime
                caseDic['pc_no'] = sampleDic['g_pc_no']
                caseDic['file'] = file
                caseDic['data'] = {}

                fiotest = imagepath + '\\fiotest.ini'
                config.clear()
                if os.path.isfile(fiotest):
                    # 处理无section格式
                    if not config.sections():
                        config[sec] = {}
                        with open(fiotest, 'r', encoding='gbk') as f:
                            for line in f:
                                if '=' in line:
                                    key, value = line.strip().split('=', 1)
                                    config['DEFAULT'][key.strip()] = value.strip()

                    for key in dataKeyLst:
                        if key.lower() in config[sec]:
                            value = config[sec][key.lower()]
                            # 去除单位
                            for unit in unitLst:
                                if unit in value:
                                    value = value[:value.find(unit)]
                                    break
                            dataDic[key] = value
                        else:
                            dataDic[key] = ''
                            if key.lower() == 'test_result':
                                dataDic[key] = 'FAIL'

                if 'qa_err_msg' in dataDic:
                    errCode = dataDic['qa_err_msg'].upper()
                    if errCode == '' or errCode == 'PASS' or errCode == 'TRUE':
                        dataDic['qa_err_msg'] = 'TRUE'

                caseDic['data'] = dataDic

                # 处理图像文件
                if '' != imageSuffix:
                    image = imagepath + '\\%s_%s' % (sec, imageSuffix)
                    if os.path.isfile(image):
                        caseDic['image'] = image
                    else:
                        caseDic['image'] = ''

def ReadMarsSmart(gDataDic, xlsSheetName, xlsTableName, xlsCaseName, marsCaseName, smartItemsListName, curpath, pattern, imageSuffix='', diskCnt=0):
    """
    专门读取Mars测试的SMART数据信息

    参数:
        gDataDic: 全局数据字典
        xlsSheetName: Excel工作表名称
        xlsTableName: Excel表格名称
        xlsCaseName: Excel列名称
        marsCaseName: Mars测试用例名称
        smartItemsListName: SMART数据项列表名称
        curpath: 当前路径
        pattern: 文件匹配模式
        imageSuffix: 图像后缀
        diskCnt: 磁盘数量限制
    """
    smartKeyLst = manager.get_list(smartItemsListName)
    config = configparser.RawConfigParser()
    strPlan = ''
    planDataDic = {}

    for file in fileLst:
        if not re.match(pattern, file):
            continue

        # 必须清除
        config.clear()
        config.read(file, encoding='gbk')
        if 'HWCONFIG' not in config.sections():
            continue
        sampleNo = config['HWCONFIG']['mms_flash']

        # 初始化数据结构
        if xlsSheetName not in gDataDic:
            gDataDic[xlsSheetName] = {}

        if xlsTableName not in gDataDic[xlsSheetName]:
            gDataDic[xlsSheetName][xlsTableName] = {}

        if strPlan == '':
            strPlan = GetPlan(file)

        if strPlan == '':
            continue

        if strPlan not in gDataDic[xlsSheetName][xlsTableName]:
            continue

        planDataDic = gDataDic[xlsSheetName][xlsTableName][strPlan]

        # 检查样片是否已存在于planDataDic中
        if sampleNo not in planDataDic:
            # 样片不存在，跳过SMART数据读取
            continue

        if marsCaseName not in config.sections():
            continue

        # 获取已存在的样片数据
        sampleDic = planDataDic[sampleNo]

        # 检查测试用例是否已存在于样片数据中
        if xlsCaseName not in sampleDic:
            # 测试用例不存在，跳过SMART数据读取
            continue

        # 获取已存在的测试用例数据
        caseDic = sampleDic[xlsCaseName]

        # 读取SMART数据并添加到现有的测试用例数据中
        smartDic = ReadSmart(config, marsCaseName, smartKeyLst)
        caseDic['smart'] = smartDic
        caseDic['smart_items_list'] = smartItemsListName



#从这样的字符串中解析出小数'1.74% (1043/60000)'
def parse_percentage(percentage_str):
    # 找到字符串中的百分号位置
    percent_index = percentage_str.find('%')
    
    # 如果找到百分号
    if percent_index != -1:
        # 从字符串开始到百分号位置的子字符串
        percent_part = percentage_str[:percent_index]
        
        # 尝试将百分比部分转换为浮点数
        try:
            # 移除可能存在的空格
            percent_part = percent_part.strip()
            # 转换为浮点数并除以100（因为百分比需要除以100）
            return float(percent_part) / 100
        except ValueError:
            print(f"无法将 {percent_part} 转换为浮点数")
            return None
    else:
        print("字符串中未找到百分号")
        return None

def parse_lifetime_format(value):
    """
    解析 Lifetime 格式的字符串，如 "1.10% (718/65000)"
    返回 (percentage, numerator, denominator) 或 None
    """
    import re
    if not value or value == '':
        return None

    # 匹配格式: "数字.数字% (数字/数字)"
    pattern = r'(\d+\.?\d*)%\s*\((\d+)/(\d+)\)'
    match = re.match(pattern, value.strip())

    if match:
        percentage = float(match.group(1))
        numerator = int(match.group(2))
        denominator = int(match.group(3))
        return (percentage, numerator, denominator)

    return None

def calculate_lifetime_delta(por_value, copy_value):
    """
    计算 Lifetime 格式的差值
    """
    por_parsed = parse_lifetime_format(por_value)
    copy_parsed = parse_lifetime_format(copy_value)

    if not por_parsed or not copy_parsed:
        return ''

    por_pct, por_num, por_den = por_parsed
    copy_pct, copy_num, copy_den = copy_parsed

    # 检查分母是否一致
    if por_den != copy_den:
        return ''  # 分母不一致，无法计算

    # 计算分子差值
    delta_num = por_num - copy_num

    # 计算新的百分比
    if por_den > 0:
        delta_pct = (delta_num / por_den) * 100
        return f"{delta_pct:.2f}% ({delta_num}/{por_den})"
    else:
        return ''

def CalculateDeltaSmartInformation(gDataDic, xlsSheetName, xlsTableName,beginCase,endCase):
    """
    计算Delta Smart Information
    根据每个sample样片数据的'POR Test'中的'SMART_DATA_ITEMS'中对应的值
    减去'Copy/Compare'中'SMART_DATA_ITEMS'对应的值得到SMART_DELTA_ITEMS中对应的各项的值
    并按照相同的结构和层级添加到每个sample样片下，其case名字为'Delta Smart Information'
    """
    if xlsSheetName not in gDataDic or xlsTableName not in gDataDic[xlsSheetName]:
        return

    # 获取SMART_DELTA_ITEMS列表
    delta_items = manager.get_list('SMART_DELTA_ITEMS')
    if not delta_items:
        return

    # 遍历所有Plan和样片
    for plan_name, plan_data in gDataDic[xlsSheetName][xlsTableName].items():
        for sample_no, sample_data in plan_data.items():
            # 获取POR Test的smart数据
            por_smart = {}
            if endCase in sample_data and 'smart' in sample_data[endCase]:
                por_smart = sample_data[endCase]['smart']

            # 获取Copy/Compare的smart数据
            copy_smart = {}
            if beginCase in sample_data and 'smart' in sample_data[beginCase]:
                copy_smart = sample_data[beginCase]['smart']

            # 计算Delta值
            delta_smart = {}
            for item in delta_items:
                por_value = GetValueFromDic(por_smart, item, '0')
                copy_value = GetValueFromDic(copy_smart, item, '0')

                # 特殊处理 SLC_Lifetime 和 TLC_Lifetime
                if item in ['SLC_Lifetime', 'TLC_Lifetime']:
                    delta_smart[item] = calculate_lifetime_delta(por_value, copy_value)
                else:
                    # 转换为数值进行计算
                    try:
                        por_num = convert_to_number(por_value) if por_value != '' else 0
                        copy_num = convert_to_number(copy_value) if copy_value != '' else 0
                        delta_value = por_num - copy_num
                        delta_smart[item] = str(delta_value)
                    except:
                        # 如果转换失败，设置为空字符串
                        delta_smart[item] = ''

            # 创建Delta Smart Information case
            if 'Delta Smart Information' not in sample_data:
                sample_data['Delta Smart Information'] = {}

            # 设置Delta Smart Information的数据结构
            sample_data['Delta Smart Information']['data'] = delta_smart
            sample_data['Delta Smart Information']['data_items_list'] = 'SMART_DELTA_ITEMS'
            #sample_data['Delta Smart Information']['data'] = {}  # 空的data字典
            #sample_data['Delta Smart Information']['data_items_list'] = []  # 空的data_items_list


def CalculateDeltaSmartInformationWithName(gDataDic, xlsSheetName, xlsTableName,deltaSmartName,beginCasePlan,beginCase,endCasePlan,endCase):
    """
    计算Delta Smart Information
    根据每个sample样片数据的指定case中的'SMART_DATA_ITEMS'中对应的值
    减去另一个指定case中'SMART_DATA_ITEMS'对应的值得到SMART_DELTA_ITEMS中对应的各项的值
    并按照相同的结构和层级添加到每个sample样片下，其case名字为deltaSmartName参数指定的值

    参数约束：
    - beginCase 必须从 plan_name 等于 beginCasePlan 的数据中选择
    - endCase 必须从 plan_name 等于 endCasePlan 的数据中选择
    """
    if xlsSheetName not in gDataDic or xlsTableName not in gDataDic[xlsSheetName]:
        return

    # 获取SMART_DELTA_ITEMS列表
    delta_items = manager.get_list('SMART_DELTA_ITEMS')
    if not delta_items:
        return

    # 遍历所有Plan和样片
    for plan_name, plan_data in gDataDic[xlsSheetName][xlsTableName].items():
        for sample_no, sample_data in plan_data.items():
            # 数据筛选约束：验证beginCase是否属于beginCasePlan
            begin_case_valid = False
            if plan_name == beginCasePlan and beginCase in sample_data:
                begin_case_valid = True
            elif plan_name != beginCasePlan:
                # 如果当前plan不是beginCasePlan，检查其他plan中是否有该sample的beginCase数据
                for other_plan_name, other_plan_data in gDataDic[xlsSheetName][xlsTableName].items():
                    if other_plan_name == beginCasePlan and sample_no in other_plan_data and beginCase in other_plan_data[sample_no]:
                        begin_case_valid = True
                        break

            # 数据筛选约束：验证endCase是否属于endCasePlan
            end_case_valid = False
            if plan_name == endCasePlan and endCase in sample_data:
                end_case_valid = True
            elif plan_name != endCasePlan:
                # 如果当前plan不是endCasePlan，检查其他plan中是否有该sample的endCase数据
                for other_plan_name, other_plan_data in gDataDic[xlsSheetName][xlsTableName].items():
                    if other_plan_name == endCasePlan and sample_no in other_plan_data and endCase in other_plan_data[sample_no]:
                        end_case_valid = True
                        break

            # 如果指定的case不属于对应的plan，则跳过，处理下一个样片sample_no
            if not begin_case_valid or not end_case_valid:
                continue

            # 获取endCase的smart数据（从endCasePlan中）
            por_smart = {}
            if plan_name == endCasePlan and endCase in sample_data and 'smart' in sample_data[endCase]:
                por_smart = sample_data[endCase]['smart']
            else:
                # 从其他plan中查找endCase数据
                for other_plan_name, other_plan_data in gDataDic[xlsSheetName][xlsTableName].items():
                    if other_plan_name == endCasePlan and sample_no in other_plan_data and endCase in other_plan_data[sample_no] and 'smart' in other_plan_data[sample_no][endCase]:
                        por_smart = other_plan_data[sample_no][endCase]['smart']
                        break

            # 获取beginCase的smart数据（从beginCasePlan中）
            copy_smart = {}
            if plan_name == beginCasePlan and beginCase in sample_data and 'smart' in sample_data[beginCase]:
                copy_smart = sample_data[beginCase]['smart']
            else:
                # 从其他plan中查找beginCase数据
                for other_plan_name, other_plan_data in gDataDic[xlsSheetName][xlsTableName].items():
                    if other_plan_name == beginCasePlan and sample_no in other_plan_data and beginCase in other_plan_data[sample_no] and 'smart' in other_plan_data[sample_no][beginCase]:
                        copy_smart = other_plan_data[sample_no][beginCase]['smart']
                        break

            # 计算Delta值
            delta_smart = {}
            for item in delta_items:
                por_value = GetValueFromDic(por_smart, item, '0')
                copy_value = GetValueFromDic(copy_smart, item, '0')

                # 特殊处理 SLC_Lifetime 和 TLC_Lifetime
                if item in ['SLC_Lifetime', 'TLC_Lifetime']:
                    delta_smart[item] = calculate_lifetime_delta(por_value, copy_value)
                else:
                    # 转换为数值进行计算
                    try:
                        por_num = convert_to_number(por_value) if por_value != '' else 0
                        copy_num = convert_to_number(copy_value) if copy_value != '' else 0
                        delta_value = por_num - copy_num
                        delta_smart[item] = str(delta_value)
                    except:
                        # 如果转换失败，设置为空字符串
                        delta_smart[item] = ''

            # 创建动态命名的case（使用deltaSmartName参数）
            if deltaSmartName not in sample_data:
                sample_data[deltaSmartName] = {}

            # 设置动态命名case的数据结构
            sample_data[deltaSmartName]['data'] = delta_smart
            sample_data[deltaSmartName]['data_items_list'] = 'SMART_DELTA_ITEMS'
            #sample_data[deltaSmartName]['data'] = {}  # 空的data字典
            #sample_data[deltaSmartName]['data_items_list'] = []  # 空的data_items_list


#'1( 16 | 00|6FE  )' badnew
def extract_number_before_parentheses(input_str):
    # 找到左括号的位置
    open_paren_index = input_str.find('(')
    
    # 如果找到左括号
    if open_paren_index != -1:
        # 提取左括号前的子字符串
        number_part = input_str[:open_paren_index]
        
        # 去除可能存在的空格
        number_part = number_part.strip()
        
        # 尝试将提取的部分转换为整数
        try:
            return int(number_part)
        except ValueError:
            print(f"无法将 '{number_part}' 转换为整数")
            return None
    else:
        print("字符串中未找到左括号")
        return None

def GetMarsTestTime(dicCase):
    endTimeStr = GetValueFromDic(dicCase,'end_time')
    startTimeStr = GetValueFromDic(dicCase,'start_time')
    if '' == endTimeStr or '' == startTimeStr:
        return ''
    else:
        try:
            endtime = datetime.strptime(endTimeStr, '%Y-%m-%d %H:%M:%S')
            starttime = datetime.strptime(startTimeStr, '%Y-%m-%d %H:%M:%S')
            totalSecond = timedelta.total_seconds(endtime-starttime)
            totalSecond = int(totalSecond)
            hour = int(totalSecond/3600)
            lefSeconds = totalSecond%3600
            minutes = int(lefSeconds/60)
            seconds = lefSeconds%60
            timeStr = '%d:%d:%d'%(hour,minutes,seconds)
            return timeStr
        except:
            return ''

def GetPlan(path):
    parts = path.split('\\')
    plan_part = next(part for part in parts if part.startswith('Plan'))  # 查找首个含"Plan"的目录
    return plan_part


def GetColumnStart(coordinates, field_name, xlsCaseName=None):
    """
    根据条件获取指定字段的起始列
    :param xlsCaseName: 表名标识，为'table_name'时取同级header_structure
    """
    if not coordinates:
        return ''

    target_structure = coordinates.get('header_structure', [])
    
    if xlsCaseName == 'table_name' and 'header_structure' in coordinates:
        target_structure = coordinates['header_structure']

    for node in target_structure:
        if isinstance(node, dict) and node.get('name') == field_name:
            return node.get('start_col', '')
    return ''

def WriteMarsDataToSheet(gDataDic, xlsSheetName, xlsTableName, worksheet): 
    if xlsSheetName not in gDataDic:
        return #没有sheet页数据

    if xlsTableName not in gDataDic[xlsSheetName]:
        return #没有sheet页内表格数据

    if xlsSheetName not in g_ExcelJsonDic:
        print('no json of xlssheet:' + xlsSheetName)
        return

    coordinates_list = g_ExcelJsonDic[xlsSheetName]

    # 根据xlsTableName查找匹配的表格配置
    target_table = None
    for table in coordinates_list:
        if table['table_name'] == xlsTableName:
            target_table = table
            break
    
    if not target_table:
        print(f"错误：未找到'{xlsTableName}'的表格配置")
        return

    # 获取数据起始行
    dataStartRow = int(target_table['row_range'][1]) + 1
    
    # 获取列映射
    column_map = {
        'No.': GetColumnStart(target_table, 'No.',xlsTableName),
        'Sample No.': GetColumnStart(target_table, 'Sample No.',xlsTableName),
        'PC No.': GetColumnStart(target_table, 'PC No.',xlsTableName),
        'Capacity (MB)': GetColumnStart(target_table, 'Capacity\n(MB)',xlsTableName)
    }
    
    # 初始化行号
    current_row = dataStartRow
    sample_num = 1
    
    # 遍历样片数据
    for Plan, plan_sample_data in gDataDic[xlsSheetName][xlsTableName].items():
        for sampleNo, sample_data in plan_sample_data.items():
            # 写入基础信息
            worksheet[f"{column_map['No.']}{current_row}"] = sample_num
            worksheet[f"{column_map['Sample No.']}{current_row}"] = sampleNo
            worksheet[f"{column_map['PC No.']}{current_row}"] = sample_data.get('g_pc_no', '')
            worksheet[f"{column_map['Capacity (MB)']}{current_row}"] = convert_to_content(sample_data.get('capacity', ''))

            #一个表格的smart是固定的，先获取Smart起始
            table_smart_start_col = ''
            for case in target_table['header_structure']:
                if 'smart' in case['name'].lower():
                    table_smart_start_col = case['start_col']
                    break

        
            # 获取case结构
            for case in target_table['header_structure']:
                case_name = case['name']
                case_start_col = case['start_col']
            
                
                data_values = sample_data.get(case_name, {}).get('data', {})
                if data_values == {}:
                    continue
                
                data_items_list = manager.get_list(sample_data.get(case_name, {}).get('data_items_list', []))
                if data_items_list == []:
                    continue
                # 写入数据项
                for idx, item in enumerate(data_items_list):
                    col_letter = get_column_letter(column_index_from_string(case_start_col) + idx)
                    worksheet[f"{col_letter}{current_row}"] = convert_to_content(data_values.get(item, ''))
            
                # 写入smart数据
                smart_values = sample_data.get(case_name, {}).get('smart', {})
                if smart_values == {}:
                    continue

                #没有列信息也要返回。
                if table_smart_start_col == '':
                    continue

                smart_items_list = manager.get_list(sample_data.get(case_name, {}).get('smart_items_list', []))
                if smart_items_list == []:
                    continue
                for idx, item in enumerate(smart_items_list):
                    col_letter = get_column_letter(column_index_from_string(table_smart_start_col) + idx)
                    worksheet[f"{col_letter}{current_row}"] = convert_to_content(smart_values.get(item, ''))
        
            current_row += 1
            sample_num += 1

def ReadSmart(config,marsCaseName,smartKeyLst):
    smartDic = {}
    unitLst = ['M/s','MB']
    for key in smartKeyLst:
        if key.lower() in config[marsCaseName]:
            value = config[marsCaseName][key.lower()]
            #去除单位
            for unit in unitLst:
                if unit in value:
                    value = value[:value.find(unit)]
                    break
            smartDic[key] = value

    #计算出需要计算的smart值，非常明确。   
    WL_SLC_MAX = GetValueFromDic(smartDic,'WL_SLC_MAX')
    WL_SLC_MIN = GetValueFromDic(smartDic,'WL_SLC_MIN')
    if WL_SLC_MAX != '' and WL_SLC_MIN != '':
        smartDic['SLC_DIFF'] = str(convert_to_number(WL_SLC_MAX)-convert_to_number(WL_SLC_MIN))

    WL_TLC_MAX = GetValueFromDic(smartDic,'WL_TLC_MAX')
    WL_TLC_MIN = GetValueFromDic(smartDic,'WL_TLC_MIN')
    if WL_TLC_MAX != '' and WL_TLC_MIN != '':
        smartDic['TLC_DIFF'] = str(convert_to_number(WL_TLC_MAX)-convert_to_number(WL_TLC_MIN))

    WL_SLC_AVG = GetValueFromDic(smartDic,'WL_SLC_AVG')
    WL_TLC_AVG = GetValueFromDic(smartDic,'WL_TLC_AVG')
    if WL_SLC_AVG != '' and WL_TLC_AVG != '':
        try:
            smartDic['WEAR_SLC_TLC'] = convert_to_number(WL_SLC_AVG)/convert_to_number(WL_TLC_AVG)
        except:
            smartDic['WEAR_SLC_TLC'] = UNDEFINE
                
    return smartDic

#简单的转换成内容格式，内容要么是空字符串，要么是int或者float其它情况一律保持原样
def convert_to_content(value):
    """
    将变量转换为数值：
    - 若为字符串且以 0x 开头（不区分大小写），转为十六进制整数。
    - 否则尝试转为整数或浮点数。
    """
    if value == '' or value == UNDEFINE:
        return value

    if isinstance(value, str):
        stripped = value.strip().lower()  # 去空格并统一为小写
        if stripped.startswith("0x"):
            return int(stripped[2:], 16)      # 十六进制转整数
        else:
            # 尝试转为整数或浮点数
            try:
                return int(stripped)
            except ValueError:
                try:
                    return float(stripped)     # 若含小数点则返回浮点数
                except:
                    return value #返回原始内容
    else:
        # 非字符串直接转为数值（如 int/float 类型）
        return float(value) if isinstance(value, float) else int(value)


def WriteToolDataToSheet(gDataDic, xlsSheetName, xlsTableName, worksheet): 
    if xlsSheetName not in gDataDic:
        return #没有sheet页数据

    if xlsTableName not in gDataDic[xlsSheetName]:
        return #没有sheet页内表格数据

    if xlsSheetName not in g_ExcelJsonDic:
        print('no json of xlssheet:' + xlsSheetName)
        return

    coordinates_list = g_ExcelJsonDic[xlsSheetName]

    # 根据xlsTableName查找匹配的表格配置
    target_table = None
    for table in coordinates_list:
        if table['table_name'] == xlsTableName:
            target_table = table
            break
    
    if not target_table:
        print(f"错误：未找到'{xlsTableName}'的表格配置")
        return

    # 获取数据起始行
    dataStartRow = target_table['table_start_row']
    
    ## 获取列映射
    #column_map = {
    #    'Sample No.': target_table['table']['sample_no'],
    #    'PC No.': target_table['table']['pc_no'],
    #    'Capacity (MB)': target_table['table']['capacity']
    #}
    
    # 初始化行号
    current_row = dataStartRow
    sample_num = 1
    table_row_offset = target_table.get('table_row_offset', {})
    table_row_gap = target_table.get('table_row_gap_one_sample', 1)

    # 遍历样片数据，写表格数据
    for Plan, plan_sample_data in gDataDic[xlsSheetName][xlsTableName].items():
        for sampleNo, sample_data in plan_sample_data.items():
            # 写入基础信息
            base_row = current_row + table_row_offset.get('sample_no', 0)
            #worksheet[f"{column_map['Sample No.']}{base_row}"] = sampleNo
            #worksheet[f"{column_map['PC No.']}{base_row}"] = sample_data.get('g_pc_no', '')
            #worksheet[f"{column_map['Capacity (MB)']}{base_row}"] = convert_to_content(sample_data.get('capacity', ''))

            # 获取case结构，写表格数据
            for case in target_table['table']:
                case_start_col = target_table['table'][case]
                row_offset = table_row_offset.get(case, 0)
                row = current_row + row_offset

                if case.lower() == 'sample_no':
                    worksheet[f"{target_table['table']['sample_no']}{base_row}"] = sampleNo
                if case.lower() == 'capacity':
                    worksheet[f"{target_table['table']['capacity']}{base_row}"] = convert_to_content(sample_data.get('capacity', ''))
                if case.lower() == 'pc_no':
                    worksheet[f"{target_table['table']['pc_no']}{base_row}"] = sample_data.get('g_pc_no', '')

                try:
                    data_values = sample_data.get(case, {}).get('data', {})
                except:
                    data_values = {}
                    
                if data_values == {}:
                    continue
                data_items_list = manager.get_list(sample_data.get(case, {}).get('data_items_list', []))
                if data_items_list == []:
                    continue
                # 写入数据项
                for idx, item in enumerate(data_items_list):
                    col_letter = get_column_letter(column_index_from_string(case_start_col) + idx)
                    worksheet[f"{col_letter}{row}"] = convert_to_content(data_values.get(item, ''))
            current_row += table_row_gap
            sample_num += 1

    #获取图像的起
    if 'image' not in target_table or 'image_start_row' not in target_table:
        return

    image_row_offset = target_table.get('image_row_offset', {})
    image_row_gap = target_table.get('image_row_gap_one_sample', 1)
    imageStartRow = target_table['image_start_row']
    imageRow = imageStartRow
    sample_num = 1
    for Plan, plan_sample_data in gDataDic[xlsSheetName][xlsTableName].items():
        for sampleNo, sample_data in plan_sample_data.items():
            for case in target_table['image']:
                imageCol = target_table['image'][case]
                row_offset = image_row_offset.get(case, 0)
                row = imageRow + row_offset
                if case == 'sample_no':
                    worksheet[f"{imageCol}{row}"] = sampleNo
                try:
                    imagepath = sample_data.get(case, {}).get('image', '')
                except:
                    imagepath = ''
                if imagepath != '':
                    img = Image(imagepath)
                    col_letter = imageCol
                    col_dim = worksheet.column_dimensions[col_letter]
                    row_dim = worksheet.row_dimensions[row]
                    col_width = col_dim.width if col_dim.width else worksheet.sheet_format.defaultColWidth if hasattr(worksheet.sheet_format, 'defaultColWidth') else 8.43
                    row_height = row_dim.height if row_dim.height else worksheet.sheet_format.defaultRowHeight if hasattr(worksheet.sheet_format, 'defaultRowHeight') else 15
                    img.width = float(col_width) * 7*4
                    img.height = float(row_height) * 1.33
                    worksheet.add_image(img, '%s%d' % (col_letter, row))
            imageRow += image_row_gap
            sample_num += 1




def GetSmartCaseFromMarsCase(gDataDic,xlsSheetName,xlsTableName,xlsCaseName,smartCaseName):